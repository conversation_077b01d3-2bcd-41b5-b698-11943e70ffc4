<template>
  <view>
    <uni-card
      class="card"
      margin="20rpx 30rpx"
      :is-shadow="true"
      shadow="0px 4px 6px -1px rgba(0,64,152,0.05),0px 0px 10px 0px rgba(0,64,152,0.05)"
    >
      <template v-slot:title>
        <view class="card-title">
          <view class="left" style="display: flex; flex-direction: row">
            <view class="icon">
              <image
                src="@/static/images/home/<USER>"
                alt="工单图标"
                style="width: 40rpx; height: 40rpx"
              />
            </view>
            <view class="title" style="margin-left: 10rpx"> 我的巡检 </view>
          </view>
          <view
            class="right"
            @click="toAllList"
            style="display: flex; flex-direction: row; align-items: center"
          >
            全部工单
            <u-icon name="arrow-right" color="rgba(0,0,0,0.6)"></u-icon>
          </view>
        </view>

        <view class="grid-body">
          <uni-grid
            :column="4"
            :showBorder="false"
            @change="changeFixGrid"
            borderColor="#81d3f8"
          >
            <uni-grid-item
              v-for="(item, index) in myMaintenanceData"
              :index="index"
              :key="index"
            >
              <view class="grid-item-box">
                <!-- v-show="permissionList.includes(item.permissionName)" -->
                <!-- <uni-icons custom-prefix="iconfont" type="icon-jilu" color="#81d3f8" size="30"></uni-icons> -->
                <image
                  :src="item.iconUrl"
                  style="width: 80rpx; height: 80rpx"
                  mode="scaleToFill"
                >
                </image>
                <text class="text">{{ item.name }}</text>
                <view class="grid-dot">
                  <uni-badge v-if="item.num" :text="item.num" type="error" />
                </view>
              </view>
            </uni-grid-item>
          </uni-grid>
        </view>
      </template>
    </uni-card>
  </view>
</template>

<script>
  import store from "@/store/modules/user.js";
  export default {
    props: ["inspectionBtnPermission", "inspectionMenuPermission"],
    data() {
      return {
        myMaintenanceData: this.inspectionBtnPermission,
        permissionList: [],
        menuPermission: this.inspectionMenuPermission,
      };
    },
    methods: {
      changeFixGrid(val) {
        console.log(val);
        const { index } = val.detail;
        getApp().globalData.inspection = {
          statusName: this.myMaintenanceData[index].name,
          status: this.myMaintenanceData[index].status,
          workOrderId: "",
          workOrderName: "",
          permissionIdentification: this.menuPermission,
          deviceId: "",
        };

        // getApp().globalData.inspection.statusName = this.myMaintenanceData[index].name
        // getApp().globalData.inspection.status = this.myMaintenanceData[index].status
        // getApp().globalData.inspection.workOrderId = ''
        // getApp().globalData.inspection.workOrderName = ''
        // getApp().globalData.inspection.permissionIdentification = this
        // 	.menuPermission
        // getApp().globalData.inspection.deviceId = ''

        let route = this.myMaintenanceData[index].route;
        let menuPermission = this.menuPermission;
        let type = this.myMaintenanceData[index].name;
        let status = this.myMaintenanceData[index].status;
        let url = `${route}?type=${type}&status=${status}&menuPermission=${menuPermission}`;
        uni.navigateTo({
          url: url,
          animationType: "pop-in",
          animationDuration: 200,
        });
      },
      toAllList() {
        getApp().globalData.inspection = {
          statusName: "全部",
          status: "0",
          workOrderId: "",
          workOrderName: "",
          permissionIdentification: this.menuPermission,
          deviceId: "",
        };
        uni.navigateTo({
          url:
            "/pages/inspection/index?type=全部&status=0" +
            "&menuPermission=" +
            this.menuPermission,
          animationType: "pop-in",
          animationDuration: 200,
        });
      },
    },
    mounted() {
      this.permissionList = store.state.permissions;
      // console.log(store.state.permissions)
    },
  };
</script>

<style>
  .grid-item-box {
    flex: 1;
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 0;
  }

  .grid-dot {
    position: absolute;
    top: 5px;
    right: 15px;
  }

  .card {
    border-radius: 24rpx;
    background: #ffffff;
    box-shadow: 0px 4px 6px -1px rgba(0, 64, 152, 0.05),
      0px 0px 10px 0px rgba(0, 64, 152, 0.05);
  }

  .card-title {
    display: flex;
    justify-content: space-between;
    margin-top: 30rpx;
    margin-left: 10rpx;

    .title {
      margin-left: 10rpx;
      font-family: MiSans, MiSans;
      font-weight: 500;
      font-size: 36rpx;
      color: rgba(0, 0, 0, 0.9);
      line-height: 45rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .left {
      display: flex;
      justify-content: space-between;

      .icon {
        image {
          width: 45rpx;
          height: 45rpx;
          vertical-align: middle;
        }
      }
    }

    .right {
      display: flex;
      justify-content: center;
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.6);
      line-height: 52rpx;
      font-style: normal;
      text-transform: none;
    }
  }

  .grid-body {
    margin-top: 20rpx;
  }
</style>
