<template>
  <view class="container">
    <!-- 顶部信息区 -->
    <view class="header">
      <view class="device-card">
        <view class="section-title">
          <image
            style="width: 40rpx; height: 40rpx"
            src="../../static/images/icon.png"
            mode="scaleToFill"
          />
          <text style="margin-left: 10rpx">立即报修</text>
        </view>
        <u-line></u-line>
        <view class="device-info">
          <image class="device-image" :src="equipmentInfo.imageUrl" />
          <view class="device-detail">
            <text class="device-name">{{ equipmentInfo.equipmentName }}</text>
            <view class="device-location">
              <uni-icons type="location" size="14" color="#666666" />
              <text class="location-text">{{
                equipmentInfo.fullConfigName
              }}</text>
            </view>
            <text class="status-text">{{
              getStatus(equipmentInfo.equipmentStatus)
            }}</text>
          </view>
          <uni-icons type="right" size="16" color="#CCCCCC" />
        </view>
      </view>

      <!-- 健康度仪表盘 -->
      <view class="health-section">
        <view class="section-title">
          <view class="title-indicator"></view>
          <text>设备健康度</text>
        </view>
        <u-line></u-line>
        <view class="health-display">
          <view class="gauge-container">
            <view class="gauge-value" :class="[colorClass]">{{
              equipmentStatus
            }}</view>
          </view>
          <text class="health-desc">设备健康度</text>
        </view>
      </view>
    </view>

    <!-- 基本信息区 -->
    <view class="info-section">
      <view class="section-title">
        <view class="title-indicator"></view>
        <text>基本信息</text>
      </view>
      <u-line></u-line>
      <view class="info-list">
        <view class="info-item">
          <text class="item-label">设备编号</text>
          <text class="item-value">{{ equipmentInfo.equipmentCode }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">设备名称</text>
          <text class="item-value">{{ equipmentInfo.equipmentName }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">设备类型</text>
          <text class="item-value">{{ typeObj }}</text>
        </view>
        <!-- <view class="info-item">
          <text class="item-label">所属部门</text>
          <text class="item-value">{{
            equipmentInfo.equipmentDepartment
          }}</text>
        </view> -->
        <view class="info-item">
          <text class="item-label">品牌</text>
          <text class="item-value">{{ equipmentInfo.equipmentBrand }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">规格型号</text>
          <text class="item-value">{{ equipmentInfo.equipmentModel }}</text>
        </view>
      </view>
    </view>

    <!-- 设备来源区 -->
    <view class="source-section">
      <view class="section-title">
        <view class="title-indicator"></view>
        <text>采购信息</text>
      </view>
      <u-line></u-line>
      <view class="info-list">
        <view class="info-item">
          <text class="item-label">采购日期</text>
          <text class="item-value">{{ equipmentInfo.buyDate }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">采购价格</text>
          <text class="item-value">{{ equipmentInfo.purchasePrice }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">供应商单位名称</text>
          <text class="item-value">{{ equipmentInfo.supplier }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">供应商联系人</text>
          <text class="item-value">{{ equipmentInfo.supplierContacts }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">供应商联系方式</text>
          <text class="item-value">{{ equipmentInfo.supplierPhone }}</text>
        </view>
      </view>
    </view>
    <view class="source-section">
      <view class="section-title">
        <view class="title-indicator"></view>
        <text>维保信息</text>
      </view>
      <u-line></u-line>
      <view class="info-list">
        <view class="info-item">
          <text class="item-label">维保单位名称</text>
          <text class="item-value">{{ equipmentInfo.maintenance }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">维保联系人</text>
          <text class="item-value">{{
            equipmentInfo.maintenanceContacts
          }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">维保联系方式</text>
          <text class="item-value">{{ equipmentInfo.maintenancePhone }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">保修日期</text>
          <text class="item-value">{{ equipmentInfo.guaranteeDate }}</text>
        </view>
      </view>
    </view>
    <view class="source-section">
      <view class="section-title">
        <view class="title-indicator"></view>
        <text>厂家信息</text>
      </view>
      <u-line></u-line>
      <view class="info-list">
        <view class="info-item">
          <text class="item-label">生产厂家</text>
          <text class="item-value">{{ equipmentInfo.equipmentFactory }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">序列号</text>
          <text class="item-value">{{ equipmentInfo.exFactoryNumber }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">出厂日期</text>
          <text class="item-value">{{ equipmentInfo.exFactoryDate }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">保修日期</text>
          <text class="item-value">{{ equipmentInfo.guaranteeDate }}</text>
        </view>
      </view>
    </view>
    <view class="source-section">
      <view class="section-title">
        <view class="title-indicator"></view>
        <text>使用信息</text>
      </view>
      <u-line></u-line>
      <view class="info-list">
        <view class="info-item">
          <text class="item-label">所属部门</text>
          <text class="item-value">{{ equipmentInfo.equipmentModel }}</text>
        </view>
        <view class="info-item">
          <text class="item-label">负责人</text>
          <text class="item-value">{{
            equipmentInfo.responsiblePersonName
          }}</text>
        </view>
      </view>
    </view>
    <u-button
      type="primary"
      color="#0052D9"
      style="margin: 40rpx auto; width: 232rpx; height: 60rpx"
      text="设备概况查询"
      @click="toOverview"
    ></u-button>
  </view>
</template>

<script>
  import {
    getEquipmentInfo,
    getHealthLevel,
  } from "@/api/reportRepair/index.js";
  import { getInspectionTaskStatusDict } from "@/api/system/dict.js";
  export default {
    data() {
      return {
        equipmentInfo: {},
        type: [],
        equipmentStatus: "",
      };
    },
    onLoad() {
      this.getEquipmentInfoFn();
      getInspectionTaskStatusDict("device_type").then((res) => {
        if (res.code == 0) {
          this.type = res.data;
        }
      });
    },
    computed: {
      typeObj() {
        return (
          this.type.find(
            (item) => item.value == this.equipmentInfo?.equipmentType
          )?.label || ""
        );
      },
      colorClass() {
        if (this.equipmentStatus == "优" || this.equipmentStatus == "良") {
          return "color-green";
        } else if (this.equipmentStatus == "中") {
          return "color-yellow";
        } else if (this.equipmentStatus == "差") {
          return "color-red";
        }
      },
    },
    methods: {
      toOverview() {
        uni.navigateTo({
          url: `/pages/equipment/equipmentOverview?id=${this.$route.query.id}&equipmentCode=${this.$route.query.equipmentCode}`,
        });
      },
      getStatus(params) {
        switch (params) {
          case 5:
            return "已报废";
          case 4:
            return "停机检修";
          case 3:
            return "过保运行";
          case 2:
            return "运行异常";
          case 1:
            return "正常运行";
          default:
            return "";
        }
      },
      async getEquipmentInfoFn() {
        const res = await getEquipmentInfo({
          id: this.$route.query.id,
        });
        this.equipmentInfo = res.data;
        getHealthLevel({
          id: this.$route.query.id,
          equipmentId: this.$route.query.equipmentCode,
        }).then((res) => {
          console.log(res);
          if (res.code == 0 || res.code == 200) {
            let obj = {
              1: "优",
              2: "良",
              3: "中",
              4: "差",
            };
            this.equipmentStatus = obj[res.data];
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    background-color: #f5f5f5;
  }

  .container {
    min-height: 100%;
    padding: 20rpx;
  }

  .header {
    margin-bottom: 20rpx;
  }

  .title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
  }

  .title {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
  }

  .device-card {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
  }

  .device-info {
    display: flex;
    align-items: center;
    margin-top: 10rpx;
  }

  .device-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 8rpx;
    flex-shrink: 0;
  }

  .device-detail {
    flex: 1;
    margin-left: 24rpx;
  }

  .device-name {
    font-size: 16px;
    color: #333333;
    margin-bottom: 12rpx;
  }

  .device-location {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
  }

  .location-text {
    font-size: 28rpx;
    color: #666666;
    margin-left: 8rpx;
  }

  .status-text {
    font-size: 28rpx;
    color: #2979ff;
  }

  .health-section {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 24rpx;
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
  }

  .title-indicator {
    width: 4rpx;
    height: 32rpx;
    background-color: #2979ff;
    margin-right: 16rpx;
  }

  .health-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 10rpx;
  }

  .gauge-container {
    width: 200rpx;
    height: 200rpx;
    border-radius: 100rpx;
    background: linear-gradient(90deg, #4caf50 0%, #ffc107 50%, #f44336 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16rpx;
  }

  .gauge-value {
    width: 180rpx;
    height: 180rpx;
    border-radius: 90rpx;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    color: #4caf50;
    font-weight: bold;
    &.color-green {
      color: #4caf50;
    }
    &.color-yellow {
      color: #ffc107;
    }
    &.color-red {
      color: #f44336;
    }
  }

  .health-status {
    font-size: 20px;
    color: #4caf50;
    font-weight: bold;
    margin-bottom: 8rpx;
  }

  .health-desc {
    font-size: 28rpx;
    color: #666666;
  }

  .info-section,
  .source-section {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
  }

  .info-list {
    padding: 0 16rpx;
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    padding: 24rpx 0;
    border-bottom: 1px solid #eeeeee;
  }

  .info-item:last-child {
    border-bottom: none;
  }

  .item-label {
    font-size: 28rpx;
    color: #666666;
    // width: 150rpx;
  }

  .item-value {
    flex: 1;
    text-align: right;
    font-size: 28rpx;
    color: #333333;
  }
</style>
