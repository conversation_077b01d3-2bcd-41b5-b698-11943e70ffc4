<template>
  <view class="device-overview">
    <!-- 数据概览卡片 -->
    <view class="overview-card">
      <image
        style="width: 40rpx; height: 40rpx"
        src="../../static/images/icon.png"
        mode="scaleToFill"
      />
      <view class="title">设备概况</view>

      <view class="overview-items">
        <view class="overview-item" v-for="(item, index) in list" :key="index">
          <text class="number">{{ item.value }}</text>
          <text class="label">{{ item.label }}</text>
        </view>
      </view>
      <view class="check-time"
        >上次巡检：{{ equipmentOverview.lastInspectionTime }}</view
      >
      <view class="status-row">
        <view class="status-item">
          <text class="count"
            >{{
              equipmentOverview.lastInspectionDesc &&
              equipmentOverview.lastInspectionDesc.inspectionItem
            }}项</text
          >
          <text class="label">巡检项</text>
        </view>
        <view class="status-item">
          <text class="count"
            >{{
              equipmentOverview.lastInspectionDesc &&
              equipmentOverview.lastInspectionDesc.normalItem
            }}项</text
          >
          <text class="label">正常项</text>
        </view>
        <view class="status-item">
          <text class="count"
            >{{
              equipmentOverview.lastInspectionDesc &&
              equipmentOverview.lastInspectionDesc.exceptionItem
            }}项</text
          >
          <text class="label">异常项</text>
        </view>
        <view class="status-item">
          <text
            class="count"
            :class="[
              equipmentOverview.lastInspectionDesc &&
              equipmentOverview.lastInspectionDesc.inspectionResult == 0
                ? 'success'
                : 'error',
            ]"
            >{{
              equipmentOverview.lastInspectionDesc &&
              equipmentOverview.lastInspectionDesc.inspectionResult == 0
                ? "正常"
                : "异常"
            }}</text
          >
          <text class="label">检查结果</text>
        </view>
      </view>
      <view class="check-time"
        >上次报修：{{ equipmentOverview.lastRepairTime }}</view
      >
      <view class="status-row">
        <view class="status-item">
          <text class="count">报修名称</text>
          <text class="label">{{
            equipmentOverview.lastRepairDesc &&
            equipmentOverview.lastRepairDesc.repairName
          }}</text>
        </view>
        <view class="status-item">
          <text class="count">报修人</text>
          <text class="label">{{
            (equipmentOverview.lastRepairDesc &&
              equipmentOverview.lastRepairDesc.userName) ||
            ""
          }}</text>
        </view>
        <view class="status-item">
          <text class="count">维修人</text>
          <text class="label">{{
            equipmentOverview.lastRepairDesc &&
            equipmentOverview.lastRepairDesc.executorName
          }}</text>
        </view>
        <view class="status-item">
          <text class="count">报修结果</text>
          <text class="label">{{
            (equipmentOverview.lastRepairDesc &&
              equipmentOverview.lastRepairDesc.repairResult) ||
            ""
          }}</text>
        </view>
      </view>
      <view class="check-time"
        >上次维保：{{ equipmentOverview.lastMaintenanceTime }}</view
      >
      <view class="status-row">
        <view class="status-item">
          <view class="count"
            ><text>{{
              (equipmentOverview.lastMaintenanceDesc &&
                equipmentOverview.lastMaintenanceDesc.maintenanceItem) ||
              0
            }}</text
            >项</view
          >
          <text class="label">维保项</text>
        </view>
        <view class="status-item">
          <view class="count"
            ><text>{{
              (equipmentOverview.lastMaintenanceDesc &&
                equipmentOverview.lastMaintenanceDesc.normalItem) ||
              0
            }}</text
            >项</view
          >
          <text class="label">正常项</text>
        </view>
        <view class="status-item">
          <view class="count"
            ><text>{{
              (equipmentOverview.lastMaintenanceDesc &&
                equipmentOverview.lastMaintenanceDesc.exceptionItem) ||
              0
            }}</text
            >项</view
          >
          <text class="label">异常项</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { getEquipmentOverview } from "@/api/reportRepair/index.js";
  export default {
    data() {
      return {
        equipmentOverview: {},
        list: [
          { label: "巡检次数", value: 0 },
          { label: "报修次数", value: 0 },
          { label: "维保次数", value: 0 },
        ],
      };
    },
    onLoad() {
      this.getEquipmentOverviewFn();
    },
    computed: {},
    methods: {
      async getEquipmentOverviewFn() {
        const res = await getEquipmentOverview({
          id: this.$route.query.id,
          equipmentId: this.$route.query.equipmentCode,
        });
        this.equipmentOverview = res.data;
        res.data.equipmentOverviewCount.map((item, index) => {
          if (item.type == 0) {
            this.list[0].value = item.count;
          } else if (item.type == 1) {
            this.list[1].value = item.count;
          } else if (item.type == 2) {
            this.list[2].value = item.count;
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  page {
    height: 100%;
    background-color: #f5f5f5;
  }

  .device-overview {
    padding: 30rpx;
  }

  .overview-card {
    display: flex;
    flex-wrap: wrap;
    padding: 30rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    .title {
      width: calc(100% - 40rpx);
      box-sizing: border-box;
      padding-left: 10rpx;
      font-weight: 400;
      font-size: 32rpx;
      color: rgba(0, 0, 0, 0.9);
    }

    .overview-items {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-top: 10rpx;
      background: #f5f8fe;
      padding: 20rpx 0;
      .overview-item {
        width: 33%;
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }
    .status-row,
    .maintain-row {
      width: 100%;
      display: flex;
      justify-content: space-between;
      background: #f5f8fe;
      padding: 20rpx;
    }
    .status-item,
    .maintain-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-weight: 400;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.4);
      .label {
        padding: 0 10rpx;
        text-align: center;
      }
      .count {
        text-align: center;
      }
      .count.success {
        color: #4caf50;
      }
      .count.error {
        color: #ec221f;
      }
    }
    .status-row:last-child {
      .status-item {
        .count {
          text {
            color: #4b4b4b;
            font-size: 40rpx;
            font-weight: 500;
          }
        }
        &:last-child {
          .count {
            text {
              color: #ec221f;
            }
          }
        }
      }
    }
  }
  .check-time {
    width: 100%;
    box-sizing: border-box;
    padding: 30rpx 0;
    font-weight: 400;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.4);
  }
  .number {
    font-weight: 500;
    font-size: 40rpx;
    color: #0052d9;
    margin-bottom: 8rpx;
  }

  .label {
    font-weight: 400;
    font-size: 28rpx;
    color: rgba(0, 0, 0, 0.4);
  }

  .status-card,
  .repair-card,
  .maintain-card {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
  }

  .count {
    font-size: 16px;
    color: #333333;
    font-weight: 500;
    margin-bottom: 8rpx;
  }

  .status-tag {
    padding: 4rpx 16rpx;
    background-color: #e8f5e9;
    border-radius: 4rpx;
  }

  .tag-text {
    font-size: 12px;
    color: #4caf50;
  }

  .repair-row {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }

  .repair-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .repair-item .label {
    font-size: 14px;
    color: #666666;
  }

  .repair-item .value {
    font-size: 14px;
    color: #333333;
  }

  .status-success {
    color: #4caf50;
  }
</style>
