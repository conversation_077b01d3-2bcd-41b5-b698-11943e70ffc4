import request from '@/utils/request'


export function getDeviceClassfy(id) {
	const data = {
		id
	}
	return request({
		url: '/devops/equipment-classify/get',
		'method': 'GET',
		'data': data
	})
}

// 提交动作
export function submitDevice(id) {
	const data = {
		id
	}
	return request({
		url: '/devops/work-order/submit',
		'method': 'GET',
		'data': data
	})
}

// 更新工单项详情
export function updateWorkDetail(id, photoUrlList, itemDetailEditReqVOList) {
	const data = {
		id,
		photoUrlList,
		itemDetailEditReqVOList
	}
	return request({
		url: '/devops/work-order-item-detail/update',
		'method': 'PUT',
		'data': data
	})
}

export function updateInspectDetail(updateInspectDetailReqVOs) {
	// const data = {
	// 	updateInspectDetailReqVOs
	// }
	return request({
		url: '/devops/app/work-order/updateInspectDetail',
		headers:{
			'Content-Type': 'application/json'
		},
		'method': 'PUT',
		'data': updateInspectDetailReqVOs
	})
}