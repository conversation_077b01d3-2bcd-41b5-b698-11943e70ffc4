export function getToday() {
  const today = new Date();
  const todayStr = today.toISOString().split("T")[0];
  return todayStr;
}
export function setArray(str) {
  if (str) {
    return JSON.parse(str);
  }
}
export function formatDate(timestamp, options = {}) {
  if (!timestamp && timestamp !== 0) return null;

  // 处理秒级时间戳
  const ts = String(timestamp).length === 10 ? timestamp * 1000 : timestamp;
  const date = new Date(ts);
  if (isNaN(date)) return null;

  const { type = "full", format = "", utc = false, separator = "-" } = options;

  // 提取时间组件
  const get = utc ? "getUTC" : "get";
  const year = date[`${get}FullYear`]();
  const month = String(date[`${get}Month`]() + 1).padStart(2, "0");
  const day = String(date[`${get}Date`]()).padStart(2, "0");
  const hours = String(date[`${get}Hours`]()).padStart(2, "0");
  const minutes = String(date[`${get}Minutes`]()).padStart(2, "0");
  const seconds = String(date[`${get}Seconds`]()).padStart(2, "0");

  // 预定义格式
  const formats = {
    date: `${year}${separator}${month}${separator}${day}`,
    time: `${hours}:${minutes}:${seconds}`,
    full: `${year}${separator}${month}${separator}${day} ${hours}:${minutes}:${seconds}`,
    iso: date.toISOString(),
    local: date.toLocaleString(),
  };

  // 自定义格式处理
  if (type === "custom" && format) {
    return format
      .replace("YYYY", year)
      .replace("MM", month)
      .replace("DD", day)
      .replace("HH", hours)
      .replace("mm", minutes)
      .replace("ss", seconds);
  }

  return formats[type] || formats.full;
}
export function getOtherDatesInMonth(bookedDates) {
  // 步骤1：提取已预约日期
  const bookedSet = new Set(bookedDates.map((d) => d.appointmentDate));

  // 步骤2：生成本月所有日期
  const year = 2025,
    month = 3; // 固定处理2025年3月
  const date = new Date(year, month - 1, 1); // 月份从0开始计算
  const result = [];

  // 生成所有日期（3月有31天）
  while (date.getMonth() === month - 1) {
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const dd = String(date.getDate()).padStart(2, "0");
    const dateStr = `${yyyy}-${mm}-${dd}`;

    // 步骤3：过滤已预约日期
    if (!bookedSet.has(dateStr)) {
      result.push(dateStr);
    }

    date.setDate(date.getDate() + 1);
  }

  return result;
}
