<template>
	<view class="page" @click="itemClick">
		<view class="itemClass">
			<view class="left">
				<u--image :showLoading="true" :src="itemData.imageUrl" width="160rpx" height="160rpx"></u--image>
			</view>
			<view class="right">
				<view class="right-top">
					<view>
						{{itemData.equipmentName}}
					</view>
				</view>
				<view class="content">
					<view class="content-item">
						<view class="item-left">设备编号：</view>
						<view class="item-right">{{itemData.equipmentCode}}</view>
					</view>
					<view class="content-item">
						<view class="item-left">使用部门：</view>
						<view class="item-right">{{getDept(itemData.equipmentDepartment)}}</view>
					</view>
					<view class="content-item">
						<view class="item-left">存放位置：</view>
						<view class="item-right">{{getLocationName(itemData.equipmentLocation) }}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getEquipmentConfigPage,
		getSimpleDeptList
	} from '@/api/reportRepair'
	export default {
		props: ['itemData'],
		data() {
			return {
				equipmentItemData: {},
				deptList: [],
				locationList: [],
			};
		},
		methods: {
			async getLocation(){
				const res = await getEquipmentConfigPage({type:'location',pageNo:1,pageSize:100})
				this.locationList = res.data.list
			},
			async getDeptList(){
				const res = await getSimpleDeptList()
				this.deptList = res.data
			},
			getDept(id) {
				return this.deptList.find(item => item.id == id)?.name
			},
			getLocationName(id) {
				return this.locationList.find(item => item.id == id)?.name
			},
			itemClick(){
				this.$emit('itemClick',this.itemData)
			}
		},
		beforeMount() {
			this.getLocation()
			this.getDeptList()
			this.equipmentItemData = this.itemData
		}
	}
</script>

<style scoped lang="scss">
	.page {
		height: 200rpx !important;
		margin: 0 40rpx;
		padding: 20rpx;
		border-bottom: 1rpx solid #E7E7E7;
	}

	.itemClass {
		height: 200rpx;
		display: flex;
		justify-content: flex-start;

		.left {
			width: 200rpx;
		}

		.right {
			display: flex;
			flex-direction: column;
			justify-content: center;
			margin-left: 30rpx;
			height: 160rpx;

			.right-top {
				font-weight: 600;
			}

			.content {
				width: 500rpx;
				border-radius: 6px 6px 6px 6px;
				margin-top: 10rpx;
				color: rgba(0, 0, 0, 0.6);

				.content-item {
					display: flex;
					justify-content: space-between;
					width: 100%;
					font-family: MiSans, MiSans;
					font-weight: 400;
					font-size: 28rpx;
					text-align: left;
					font-style: normal;
					text-transform: none;

					.item-left {
						width: 30%;
					}

					.item-right {
						width: 70%;
					}
				}

				:nth-child(1) {
					margin-top: 0rpx;
				}
			}
		}

	}
</style>