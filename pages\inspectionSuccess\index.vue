<template>
  <view class="work-container">
    <topInfo
      title="任务执行"
      url="/pages/index"
      :isShowBack="false"
      backtype="switch"
    ></topInfo>
    <view
      class="totalInfo"
      style="
        height: 80vh;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
      "
    >
      <image
        src="../../static/images/inspectionSuccess/success.png"
        mode="fill"
        style="width: 200px; height: 200px"
      ></image>
      <text style="color: black; font-size: 20px">操作成功</text>
    </view>
    <view
      class=""
      style="
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-top: 20px;
        height: 50px;
      "
    >
      <button
        @click="backFirstPage"
        class="mini-btn"
        type="primary"
        size="mini"
        style="
          width: 80%;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 10px;
        "
      >
        返回首页
      </button>
    </view>
  </view>
</template>

<script>
  export default {
    onLoad: function (option) {
      //option为object类型，会序列化上个页面传递的参数
    },
    data() {
      return {};
    },
    methods: {
      backFirstPage() {
        uni.navigateBack({ delta: 2 });
        // uni.navigateTo({
        //   url: "/pages/inspection/index?type=执行中&status=10&menuPermission=mobile-menu-myInspection",
        // });
      },
    },
  };
</script>

<style lang="scss" scoped>
  page {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background-color: #fff;
    // min-height: 100%;
    height: auto;
    background-color: #f2f2f2;
  }

  .work-container {
    height: 100vh;
  }

  /deep/ .uni-card .uni-card__content[data-v-19622063] {
    padding: 0 10px 10px 10px !important;
    font-size: 14px;
    color: #6a6a6a;
    line-height: 22px;
  }

  /deep/ .uni-card {
    margin: 10px;
    padding: 0 !important;
    border-radius: 4px;
    overflow: hidden;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
      Microsoft YaHei, SimSun, sans-serif;
    background-color: #fff;
    flex: 1;
  }

  /deep/.uni-card
    .uni-card__header
    .uni-card__header-content
    .uni-card__header-content-title {
    font-size: 16px;
    color: #3a3a3a;
    font-weight: 550;
  }

  /deep/ .uni-card .uni-card__header {
    display: flex;
    border-bottom: 1px #ebeef5 solid;
    flex-direction: row;
    align-items: center;
    // padding: 10px;
    overflow: hidden;
    background-color: #f6f8fe;
  }

  view {
    font-size: 14px;
    line-height: inherit;
  }

  .text {
    text-align: center;
    font-size: 20rpx;
    margin-top: 10rpx;
  }

  .search-title {
    margin-top: 30rpx;
    display: flex;
    height: 80rpx;
    width: 100%;
    align-items: center;
    justify-content: center;
    padding: 0 10px 0 0;
  }

  .searchBar {
    width: 100%;
  }

  .totalInfo {
    margin-top: 10px;
    padding: 6px;
  }

  .totalDetailInfo {
    padding: 0 6px 6px 6px;
  }

  .infoNum {
    font-size: 18px;
    font-weight: 550;
    text-align: center;
    margin-top: 13px;
  }

  .slot-collapse {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 40px;
    align-items: center;
    padding: 0 10rpx;
    justify-content: space-between;
  }

  .slot-content {
    width: 100%;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: left;
  }

  .basicInfoTopStyle {
    background-color: #f6f8fe;
    padding: 10px 10px 10px 10px;
  }

  .basicInfoDetail {
    display: flex;
    flex-direction: row;
    justify-content: left;
    align-items: center;
    margin-top: 4px;
  }

  .basicInfoTitle {
    color: rgba(0, 0, 0, 0.4);
  }

  .basicInfoContent {
    color: #343536;
  }

  .processInfo {
    margin-top: 10px;
    background: rgba(14, 74, 224, 0.04);
  }

  .bottom-button {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    height: 80rpx;
    margin-top: 10rpx;
  }
</style>
