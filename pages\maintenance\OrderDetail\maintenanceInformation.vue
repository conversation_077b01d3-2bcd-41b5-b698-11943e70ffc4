<template>
	<view class="page">
		<uni-card class="card" v-if="isShowInfo">
			<view class="content">
				<view class="content-item">
					<view class="item-left">维修日期</view>
					<view class="item-right">{{baseInfo.repairTime}}</view>
				</view>
				<view class="content-item">
					<view class="item-left">故障原因</view>
					<view class="item-right">{{baseInfo.faultReason}}</view>
				</view>
				<view class="content-item">
					<view class="item-left">维修程度</view>
					<view class="item-right">{{baseInfo.repairDegree}}</view>
				</view>
				<view class="content-item">
					<view class="item-left">维修内容</view>
					<view class="item-right">{{baseInfo.repairContent}}</view>
				</view>
				<view class="content-item">
					<view class="item-left">维修结果</view>
					<view class="item-right">{{baseInfo.repairResult}}</view>
				</view>
				<view class="content-item">
					<view class="item-left">维修人员</view>
					<view class="item-right">{{baseInfo.executorName}}</view>
				</view>
				<view class="content-item">
					<view class="item-left">联系方式</view>
					<view class="item-right">
						<image src="@/static/images/location.png" alt="工单图标" style="width: 30rpx;height: 30rpx;vertical-align: top;" />
						<span href="javascript:;" style="color: #0E4AE0;" @click="phoneClick(baseInfo.repairTime)">{{baseInfo.contactPhone}}</span>
					</view>
				</view>
				<view class="content-item">
					<view class="item-left">签到地址</view>
					<view class="item-right">{{baseInfo.signAddress}}</view>
				</view>
				<view class="content-item" style="text-align: left;">
					<view>
						现场照片
						<view class="album">
							<view class="album__content">
								<u-album :urls="baseInfo.photoUrlList" multipleSize='180rpx'></u-album>
							</view>
						</view>
					</view>
				</view>
			</view>
		</uni-card>
		<u-empty
		        mode="data"
				icon='https://cdn.uviewui.com/uview/empty/list.png'
				v-else
		>
		</u-empty>
	</view>
</template>

<script>
	import {getRepairInfo} from '@/api/reportRepair/index.js'
	export default {
		data() {
			return {
				baseInfo:{
					repairTime:'',
					faultReason:'',
					repairDegree:'',
					repairContent:'',
					repairResult:'',
					contactPhoto:'',
					signAddress:'',
					photoUrlList:[]
				},
				isShowInfo:false,
				urls2: [
					'https://cdn.uviewui.com/uview/album/1.jpg',
					'https://cdn.uviewui.com/uview/album/2.jpg',
					'https://cdn.uviewui.com/uview/album/3.jpg',
					'https://cdn.uviewui.com/uview/album/4.jpg',
				],
			}
		},
		methods: {
			phoneClick(val) {
				uni.makePhoneCall({
					phoneNumber: val //手机号码
				})
			},
			async getRepairInfo(){
				const pages = getCurrentPages(); // 获取页面栈
				const currentPage = pages[pages.length - 1]; // 获取当前页面实例
				const options = currentPage.options; // options 就是路由参数对象
				const {data} = await getRepairInfo({
					workOrderId:options.workOrderId
				})
				if(data){
					this.isShowInfo = true
					this.baseInfo = {...this.baseInfo,...data,photoUrlList:data.photoUrlList?data.photoUrlList:[]}
				}else{
					this.isShowInfo = false
				}
				
			}
		},
		mounted() {
			this.getRepairInfo()
		}
	}
</script>

<style lang="scss">
	.card {
		border-radius: 10rpx;
		background: #FFFFFF;
		box-shadow: 0px 4px 6px -1px rgba(0, 64, 152, 0.05), 0px 0px 10px 0px rgba(0, 64, 152, 0.05);
	}

	.content {
		width: 100%;
		height: 100%;

		.content-item {
			margin-top: 30rpx;
			display: flex;
			justify-content: space-between;
			width: 100%;
			font-family: MiSans, MiSans;
			font-size: 28rpx;
			color: rgba(0, 0, 0, 0.9);
			line-height: 30rpx;
			text-align: right;
			font-style: normal;
			text-transform: none;
			border-bottom: 1rpx solid #E7E7E7;
			padding-bottom: 30rpx;

			.item-left {
				// width: 40%;
			}

			.item-right {
				width: 75%;
				// padding-left: 30rpx;
				height: 100%;

				img {
					vertical-align: bottom;
					margin-right: 5rpx
				}
			}
		}

		:nth-last-child(1) {
			border: 0;
		}
	}

	.album {
		@include flex;
		align-items: flex-start;
		margin-top: 30rpx;
		width: 100%;

		&__content {
			margin-left: 10rpx;
			flex: 1;
		}
	}
</style>