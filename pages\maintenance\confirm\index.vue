<template>
  <view class="page">
    <topInfo
      title="到场确认"
      backtype="navigateBack"
      :isShowBack="true"
      :isDark="true"
      backgroundColor="rgba(1.0,1.0,1.0,0.0)"
      :isShadow="false"
    ></topInfo>
    <uni-card class="card">
      <view class="content">
        <view class="content-item">
          <view class="item-left">到场时间：</view>
          <view class="item-right">{{ currentTime }}</view>
        </view>
        <view>
          <u-form>
            <u-form-item
              label-width="200rpx"
              label="确认设备:"
              required
              prop="equipmentName"
              @click="equipmentClick"
              ref="item4"
            >
              <u--input
                v-model="equipmentName"
                disabled
                disabledColor="#ffffff"
                placeholder="请选择设备"
                border="none"
                inputAlign="right"
                suffixIcon="arrow-right"
              ></u--input>
            </u-form-item>
          </u-form>
        </view>
      </view>
    </uni-card>
    <u-toast ref="uToast" />
    <u-tabbar :fixed="true" :placeholder="true" :safeAreaInsetBottom="true">
      <u-button
        color="#0E4AE0"
        type="primary"
        text="提交"
        style="height: 70rpx; margin: 6px 10px"
        @click="submit"
      ></u-button>
    </u-tabbar>
  </view>
</template>

<script>
  import {
    getMyReportRepairDetail,
    sceneConfirmation,
  } from "@/api/reportRepair/index.js";
  export default {
    components: {},
    data() {
      return {
        currentTime: "",
        equipmentName: "",
        equipmentId: "",
      };
    },
    methods: {
      equipmentClick() {
        uni.navigateTo({
          url:
            "/pages/maintenance/confirm/equipment?workOrderId=" +
            this.workOrderId,
        });
      },
      async submit() {
        if (!this.equipmentId) {
          return this.$refs.uToast.show({
            message: "请选择设备",
            type: "warning",
          });
        }
        const { code } = await sceneConfirmation({
          id: this.workOrderId,
          equipmentId: this.equipmentId,
        });
        if (!code) {
          this.$refs.uToast.show({
            message: "提交成功",
            type: "success",
            complete: () => {
              uni.navigateBack({
                delta: 2,
              });
            },
          });
        }
      },
    },
    mounted() {
      this.currentTime = new Date().toLocaleString();
    },
    onLoad(option) {
      this.workOrderId = option.workOrderId;
      if (option.equipmentId && option.equipmentName) {
        this.equipmentName = option.equipmentName;
        this.equipmentId = option.equipmentId;
      }
    },
    onShow() {
      const params = uni.getStorageSync("navigateBackParams");
      this.equipmentName = params.equipmentName;
      this.equipmentId = params.equipmentId;
      uni.removeStorageSync("navigateBackParams");
    },
  };
</script>

<style lang="scss" scoped>
  .page {
    height: 1200rpx;
    background-image: url(@/static/images/home/<USER>
    background-size: contain;
    /* 背景图等比缩放充整个容器 */
    background-position: center;
    /* 背景图居中对齐 */
  }

  .content {
    width: 100%;
    height: 100%;

    .content-item {
      margin-top: 20rpx;
      display: flex;
      justify-content: space-between;
      width: 100%;
      font-family: MiSans, MiSans;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.9);
      line-height: 30rpx;
      text-align: right;
      font-style: normal;
      text-transform: none;
      border-bottom: 1rpx solid #e7e7e7;
      padding-bottom: 20rpx;

      .item-left {
        // width: 40%;
      }

      .item-right {
        width: 75%;
        // padding-left: 30rpx;
        height: 100%;

        img {
          vertical-align: bottom;
          margin-right: 5rpx;
        }
      }
    }

    :nth-last-child(1) {
      border: 0;
    }
  }
</style>
