<template>
	<view>
		<tui-cascade-selection request height="200px" :itemList="itemList" :receiveData="receiveData"
			@complete="complete" @change="change"></tui-cascade-selection>
	</view>
</template>

<script>
	import tuiCascadeSelection from "@/components/thorui/tui-cascade-selection/tui-cascade-selection.vue"
	import {getEquipmentConfigTree } from '@/api/reportRepair'
	export default {
		components: {
			tuiCascadeSelection
		},
		data() {
			return {
				itemList: [],
				receiveData: [],
				webURL: 'https://thorui.cn'
			}
		},
		methods: {
			change(e) {
				/**
					 subText: '30人'  //选中项数据
					 text: '高一(3)班'
					 value: 103 //选中项value数据
				 * */

				// 模拟请求
				let value = e.value;
				let childList = e.childList;
				if (childList.length === 0) {
					//实际中以请求数据为准，无下级数据则传空数组
					this.receiveData = [];
				} else {
					uni.showLoading({
						title: '请稍候...'
					});
					setTimeout(() => {
						uni.hideLoading();
						//请求完成后将数据处理成以下格式，传入，最后一级没有则传空数组
						this.receiveData = childList.map(item=>{
							return {
								childList:item.childList,
								text:item.name,
								value:item.id,
							}
						})	
					}, 0);
				}
			},
			complete(e) {
				this.$emit('selectData',e.value,e.result.map(item=>item.text).join('/'))
			},
			// 获取报修区域树形结构
			async getEquipmentConfigData(){
				const res = await getEquipmentConfigTree({type:'breakdown'})
				this.itemList = res.data.map(item=>{
					return {
						childList:item.childList,
						text:item.name,
						value:item.id,
					}
				})
			}
		},
		onLoad() {
		},
		mounted() {
			this.getEquipmentConfigData()
		}
	}
</script>

<style>
</style>