<template>
  <view class="page">
    <view class="search-content">
      <u--input
        placeholder="请输入设备名称"
        v-model="params.equipmentName"
        prefixIcon="search"
        shape="circle"
        prefixIconStyle="font-size: 44rpx;color: #909399"
        @blur="getEquipmentList"
      ></u--input>
      <div class="icon" @click="handleScan"></div>
    </view>
    <uni-data-select
      v-model="value"
      :localdata="range"
      placeholder="设备状态"
      @change="change"
    ></uni-data-select>
    <u-list
      class="list"
      :loading="loading"
      :isLoadMore="isLoadMore"
      @scrolltolower="scrolltolower"
      :pagingEnabled="true"
    >
      <u-list-item
        v-for="(item, index) in indexList"
        :key="index"
        @click.native="itemClick(item)"
      >
        <view class="itemClass">
          <view class="left">
            <u--image
              :showLoading="true"
              :src="item.imageUrl"
              width="160rpx"
              height="160rpx"
            ></u--image>
          </view>
          <view class="right">
            <view class="right-top">
              <view>
                {{ item.equipmentName }}
              </view>
            </view>
            <view class="content">
              <view class="content-item">
                <view class="item-left">设备编号：</view>
                <view class="item-right">{{ item.equipmentCode }}</view>
              </view>
              <!-- <view class="content-item">
                <view class="item-left">使用部门：</view>
                <view class="item-right">{{
                  getDept(item.equipmentDepartment)
                }}</view>
              </view> -->
              <view class="content-item">
                <view class="item-left">设备状态：</view>
                <view class="item-right">{{
                  getStatus(item.equipmentStatus)
                }}</view>
              </view>
              <view class="content-item">
                <view class="item-left">存放位置：</view>
                <view class="item-right">{{ getLocationName(item.id) }}</view>
              </view>
            </view>
          </view>
        </view>
      </u-list-item>
    </u-list>
  </view>
</template>

<script>
  import {
    getEquipmentPage,
    getEquipmentConfigPage,
    getSimpleDeptList,
  } from "@/api/reportRepair/index.js";
  import equipmentItem from "./equipmentItem.vue";
  export default {
    components: {
      equipmentItem,
    },
    data() {
      return {
        indexList: [],
        params: {
          pageNo: 1,
          pageSize: 15,
          equipmentName: "",
          equipmentStatus: "",
        },
        isLoadMore: true, // 是否可以加载更多数据
        loading: false,
        currentItem: null,
        statusBarHeightRpx: 0, // 状态栏高度 (rpx)
        contentHeightRpx: 0, // 内容区域高度 (rpx)
        deptList: [],
        locationList: [],
        value: "",
        range: [
          { value: 5, text: "已报废" },
          { value: 4, text: "停机检修" },
          { value: 3, text: "过保运行" },
          { value: 2, text: "运行异常" },
          { value: 1, text: "正常运行" },
        ],
      };
    },
    methods: {
      itemClick(item) {
        console.log(item);
        this.currentItem = item;
        uni.navigateTo({
          url: `/pages/equipment/equipmentDetail?id=${item.id}&equipmentCode=${item.equipmentCode}`,
        });
      },
      scrolltolower(scrollTop) {
        this.loadmore();
      },
      async getEquipmentList() {
        this.params.pageNo = 1;
        const {
          data: { list },
        } = await getEquipmentPage(this.params);
        this.indexList = list;
      },
      async loadmore() {
        if (this.loading || !this.isLoadMore) return;
        this.loading = true;
        try {
          // 将新数据追加到列表中
          const {
            data: { list },
          } = await getEquipmentPage(this.params);
          this.indexList = [...this.indexList, ...list];
          // 判断是否还有更多数据
          if (list.length < this.params.pageSize) {
            // 假设接口每页返回10条数据
            this.isLoadMore = false;
          } else {
            this.params.pageNo += 1;
          }
        } catch (error) {
          console.error("加载数据出错：", error);
        } finally {
          this.loading = false;
        }
      },
      async getLocation() {
        const res = await getEquipmentConfigPage({
          type: "location",
          pageNo: 1,
          pageSize: 100,
        });
        this.locationList = res.data.list;
      },
      async getDeptList() {
        const res = await getSimpleDeptList();
        this.deptList = res.data;
      },
      getDept(id) {
        return this.deptList.find((item) => item.id == id)?.name;
      },
      getLocationName(id) {
        return this.locationList.find((item) => item.id == id)?.name;
      },
      change(e) {
        this.params.equipmentStatus = e;
        this.getEquipmentList();
      },
      getStatus(params) {
        switch (params) {
          case 5:
            return "已报废";
          case 4:
            return "停机检修";
          case 3:
            return "过保运行";
          case 2:
            return "运行异常";
          case 1:
            return "正常运行";
          default:
            return "";
        }
      },
      async handleScan() {
        if (process.env.VUE_APP_PLATFORM === "h5") {
          this.handleH5Scan(); // H5使用第三方扫码库
        } else if (
          process.env.VUE_APP_PLATFORM === "app-plus" ||
          process.env.VUE_APP_PLATFORM === "mp-weixin"
        ) {
          this.handleNativeScan(); // App/小程序使用uni.scanCode
        }
      },

      // 原生扫码（App/小程序）
      async handleNativeScan() {
        try {
          const res = await uni.scanCode({ scanType: ["qrCode"] });
          this.parseResult(res.result);
        } catch (err) {
          console.error("扫码失败:", err);
        }
      },

      // H5环境使用vue-qrcode-reader
      handleH5Scan() {
        // 需先安装插件：npm install vue-qrcode-reader
        // import { QrcodeStream } from "vue-qrcode-reader";
        // 在组件中引入并实现扫码逻辑（参考插件文档）
      },
    },
    mounted() {
      const systemInfo = uni.getSystemInfoSync();

      // 获取状态栏高度
      const statusBarHeightPx = systemInfo.statusBarHeight;
      const screenWidth = systemInfo.screenWidth;

      // 转换为 rpx
      this.statusBarHeightRpx = (750 / screenWidth) * statusBarHeightPx;

      // 计算 TabBar 高度和内容区域高度
      const screenHeight = systemInfo.screenHeight;
      const windowHeight = systemInfo.windowHeight;
      const tabBarHeightPx = screenHeight - windowHeight;
      const tabBarHeightRpx = (750 / screenWidth) * tabBarHeightPx;

      // 内容高度 = windowHeight 转换为 rpx
      this.contentHeightRpx = (750 / screenWidth) * windowHeight;
      this.loadmore();
      this.getLocation();
      this.getDeptList();
    },
  };
</script>

<style lang="scss" scoped>
  .page {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 100rpx);
    padding: 0 24rpx;
    background: rgba(0, 0, 0, 0.02);
    .search-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      .icon {
        width: 50rpx;
        height: 50rpx;
        background: url("~@/static/images/sm.png") no-repeat;
        background-size: 100% 100%;
      }
      /deep/.u-input {
        width: 636rpx;
        height: 60rpx;
        background: #ffffff;
        border-radius: 8rpx !important;
        border: 0rpx solid #dcdcdc;
        flex: unset;
        margin: 10rpx 0rpx;
        padding: 0 0 0 12rpx !important;
      }
    }
    .uni-stat__select {
      max-width: 180rpx;
      height: 50rpx;
      flex: unset;
      margin: 10rpx 0;

      /deep/.uni-select {
        border: none;
      }
    }
    .list {
      flex: 1;
      padding: 20rpx 0;
      overflow-y: auto;
      height: unset !important;
      .itemClass {
        width: 702rpx;
        height: 236rpx;
        background: #ffffff;
        box-shadow: 0rpx 8rpx 12rpx -2rpx rgba(0, 64, 152, 0.05),
          0rpx 0rpx 20rpx 0rpx rgba(0, 64, 152, 0.05);
        border-radius: 6rpx;
        margin: 10rpx 0;
        padding: 20rpx;
        display: flex;
        justify-content: flex-start;

        .left {
          width: 200rpx;
          display: flex;
          align-items: center;
        }

        .right {
          display: flex;
          flex-direction: column;
          justify-content: center;
          margin-left: 30rpx;
          // height: 160rpx;

          .right-top {
            font-weight: 600;
          }

          .content {
            width: 500rpx;
            border-radius: 6px 6px 6px 6px;
            margin-top: 10rpx;
            color: rgba(0, 0, 0, 0.6);

            .content-item {
              display: flex;
              justify-content: space-between;
              width: 100%;
              font-family: MiSans, MiSans;
              font-weight: 400;
              font-size: 28rpx;
              text-align: left;
              font-style: normal;
              text-transform: none;

              .item-left {
                width: 30%;
              }

              .item-right {
                width: 70%;
              }
            }

            :nth-child(1) {
              margin-top: 0rpx;
            }
          }
        }
      }
    }
  }
</style>
