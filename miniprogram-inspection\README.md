# 巡检管理模块

这个文件夹包含了从uni-app项目中提取的巡检管理相关的所有页面、API接口和组件代码，用于迁移到小程序项目中。

## 目录结构

```
miniprogram-inspection/
├── pages/                    # 页面文件
│   ├── inspection/           # 巡检列表页面
│   ├── inspectionDetail/     # 巡检详情页面
│   ├── inspectionExecute/    # 巡检执行页面
│   ├── inspectionHome/       # 巡检首页组件
│   ├── inspectionSuccess/    # 巡检成功页面
│   └── inspectorDetail/      # 巡检员详情页面
├── api/                      # API接口
│   ├── inspection/           # 巡检相关API
│   ├── inspectionDetail/     # 巡检详情API
│   ├── inspectionExecute/    # 巡检执行API
│   ├── taskDetail/           # 任务详情API
│   └── system/               # 系统相关API
├── components/               # 组件
│   └── topInfo/              # 顶部信息组件
├── utils/                    # 工具函数
├── static/                   # 静态资源
│   └── images/               # 图片资源
└── README.md                 # 说明文档
```

## 主要功能模块

### 1. 巡检列表 (inspection)
- 显示巡检工单列表
- 支持按状态筛选（待执行、执行中、已完成等）
- 支持搜索功能

### 2. 巡检详情 (inspectionDetail)
- 显示巡检工单详细信息
- 支持派单、退回、重新派单等操作
- 显示工单流程状态

### 3. 巡检执行 (inspectionExecute)
- 巡检任务执行界面
- 支持设备巡检项目填写
- 支持拍照上传
- 支持巡检结果提交

### 4. 巡检首页 (inspectionHome)
- 包含"我的巡检"和"巡检管理"两个组件
- 显示巡检统计信息
- 快速入口功能

### 5. 巡检成功 (inspectionSuccess)
- 巡检完成后的成功提示页面
- 返回首页功能

### 6. 巡检员详情 (inspectorDetail)
- 巡检员信息详情页面

## API接口说明

### inspection.js
- `getInspectionInfo()` - 获取巡检信息
- `getStatistics()` - 获取统计数据

### inspectionDetail.js
- `getWorkOrderDetail()` - 获取工单详情
- `getWorkOrderInspect()` - 获取工单巡检信息
- `assignment()` - 派单
- `returnHeadPerson()` - 退回负责人
- `againAssignment()` - 重新派单
- `startExecution()` - 开始执行
- `beoverdueExecution()` - 逾期执行

### inspectionExecute.js
- `getDeviceClassfy()` - 获取设备分类
- `submitDevice()` - 提交设备巡检
- `updateWorkDetail()` - 更新工单详情
- `updateInspectDetail()` - 更新巡检详情

### taskDetail.js
- `getEquipmentInspectDetailByHeadPerson()` - 获取设备巡检详情

## 迁移到小程序注意事项

1. **页面路由调整**
   - uni-app的路由需要改为小程序的路由方式
   - 页面跳转方法需要调整

2. **组件引用**
   - uni-app的组件需要改为小程序原生组件或自定义组件
   - uview-ui组件需要替换为小程序兼容的组件

3. **API请求**
   - 请求方法需要改为小程序的wx.request
   - 请求拦截器需要重新实现

4. **样式调整**
   - scss样式需要转换为小程序支持的样式
   - 部分CSS属性可能需要调整

5. **生命周期**
   - uni-app的生命周期需要改为小程序的生命周期
   - onLoad、onShow等方法需要调整

6. **权限管理**
   - 小程序的权限管理机制需要重新实现
   - 用户认证和授权流程需要调整

## 依赖的第三方库

- moment.js - 时间处理
- crypto-js - 加密处理
- thorui-uni - UI组件库（需要替换为小程序兼容版本）
- uview-ui - UI组件库（需要替换为小程序兼容版本）

## 使用建议

1. 建议先从简单的页面开始迁移，如inspectionSuccess页面
2. 逐步迁移复杂的功能页面，如inspectionExecute页面
3. 最后处理首页和列表页面的复杂交互
4. 建议保持原有的业务逻辑不变，只调整技术实现方式
