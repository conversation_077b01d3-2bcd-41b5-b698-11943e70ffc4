<template>
  <view class="">
    <topInfo
      title="任务详情"
      :url="prePageUrl"
      :isShowBack="true"
      backtype="navigate"
      animation="true"
    ></topInfo>
    <view class="totalInfo">
      <view class="" style="margin-top: 20rpx; height: 25vh">
        <uni-card type="line" is-full>
          <template v-slot:title>
            <view
              class=""
              style="
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                border-bottom: 1px solid #f2f2f2;
                padding: 20rpx 0;
              "
            >
              <view class="">
                <view class="" style="font-size: 16px; font-weight: 550">{{
                  workOrderName
                }}</view>
                <view
                  class=""
                  style="
                    font-size: 13px;
                    color: rgba(0, 0, 0, 0.4);
                    margin-top: 12rpx;
                  "
                >
                  {{ workOrderDetail.workOrderCode }}
                </view>
              </view>
              <view class="">
                <view
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    margin-right: 20rpx;
                  "
                >
                  <uni-tag
                    style="margin-left: 10rpx"
                    :text="statusName"
                    type="warning"
                    :style="{
                      backgroundColor:
                        statusName == '待派单'
                          ? '#fff6e6'
                          : statusName == '待执行'
                          ? '#e7edfc'
                          : statusName == '已逾期'
                          ? '#feebec'
                          : statusName == '已退回'
                          ? '#e6e6e6'
                          : statusName == '已完成'
                          ? '#e6f8f0'
                          : '',
                      color:
                        statusName == '待派单'
                          ? '#eeaa43'
                          : statusName == '待执行'
                          ? '#0e4ae0'
                          : statusName == '已逾期'
                          ? '#f64654'
                          : statusName == '已退回'
                          ? '#313131'
                          : statusName == '已完成'
                          ? '#20b979'
                          : '',
                      borderColor:
                        statusName == '待派单'
                          ? '#fff6e6'
                          : statusName == '待执行'
                          ? '#e7edfc'
                          : statusName == '已逾期'
                          ? '#feebec'
                          : statusName == '已退回'
                          ? '#e6e6e6'
                          : statusName == '已完成'
                          ? '#e6f8f0'
                          : '',
                    }"
                  ></uni-tag>
                </view>
              </view>
            </view>
          </template>
          <view class="basicInfo">
            <view class="basicInfoDetail">
              <view class="basicInfoTitle">维保时间:</view>
              <view class="basicInfoContent"
                >{{ workOrderDetail.frequencyTime || "" }}
              </view>
            </view>
            <view class="basicInfoDetail">
              <view class="basicInfoTitle">截止日期:</view>
              <view class="basicInfoContent"
                >{{ workOrderDetail.deadline || "" }}
              </view>
            </view>
            <view class="basicInfoDetail">
              <view class="basicInfoTitle">维保人:</view>
              <view class="basicInfoContent">{{
                workOrderDetail.executorName
              }}</view>
            </view>
            <view class="basicInfoDetail">
              <view class="basicInfoTitle">负责人:</view>
              <view class="basicInfoContent">{{
                workOrderDetail.headPersonName
              }}</view>
            </view>
            <view class="basicInfoDetail">
              <view class="basicInfoTitle">维保要求:</view>
              <view class="basicInfoContent">{{
                workOrderDetail.inspectRequire
              }}</view>
            </view>
            <view class="basicInfoBottom">
              <uni-icons
                custom-prefix="iconfont"
                type="icon-tixing"
                size="18"
                color="#f3a73f"
                style="border: 2rpx dashed #f3a73f"
              ></uni-icons>
              <text style="margin-left: 16rpx"
                >请于{{ workOrderDetail.completeTime }}前完成维保</text
              >
            </view>
          </view>
        </uni-card>
      </view>
    </view>
    <view class="detailInfo">
      <uni-segmented-control
        :current="current"
        :values="items"
        @clickItem="onClickItem"
        styleType="text"
        activeColor="#0e4ae0"
      ></uni-segmented-control>
      <scroll-view class="content" scroll-y="" style="height: 42vh">
        <view v-show="current === 0" style="padding: 20rpx 20rpx 0 20rpx">
          <uni-collapse
            ref="collapse"
            v-for="(item, index) in workOrderInspect"
            style="margin-top: 30rpx"
            accordion
          >
            <uni-collapse-item titleBorder="none" :open="true">
              <template v-slot:title>
                <view class="slot-content">
                  <view class="" style="border-left: 8rpx #0e4ae0 solid">
                    <text style="margin-left: 20rpx">{{
                      item.equipmentLocation
                    }}</text>
                  </view>
                </view>
              </template>
              <view
                class="slot-content-content"
                v-for="(floorItem, floorIndex) in item.workOrderInspectVOList"
              >
                <view
                  class="slot-content-content-content"
                  @click="contentDetail(floorItem)"
                >
                  <view class="">
                    <text style="margin-left: 20rpx">{{ floorItem.name }}</text>
                  </view>
                  <view class="">
                    <text
                      v-if="floorItem.status == 0"
                      style="margin-left: 20rpx"
                      >{{ floorItem.value }}</text
                    >
                    <text
                      v-if="floorItem.status == 1"
                      style="margin-left: 20rpx; color: #0079fe"
                      >{{ floorItem.value }}</text
                    >
                  </view>
                  <!-- 									<view v-if="statusName != '处理中' && statusName != '已完成'" class=""
										style="color: #1a1a1a;">
										{{floorItem.inspectNumber}}项
									</view>
									<view v-if="statusName == '处理中' || statusName == '已完成'" class=""
										style="color: #1a1a1a;display: flex;flex-direction: row;align-items: center;">
										<view class=""
											style="width: 20rpx;height: 20rpx;background-color: #04b068;border-radius: 20rpx;">
										</view>
										<view class="" style="margin-left: 8rpx;">
											{{floorItem.normalNumber ||  0}}
										</view>
										<view class=""
											style="width: 20rpx;height: 20rpx;background-color: #f52f3e;margin-left: 16rpx;border-radius: 20rpx;">
										</view>
										<view class="" style="margin-left: 8rpx;">
											{{floorItem.exceptionNumber}}
										</view>

									</view> -->
                </view>
              </view>
            </uni-collapse-item>
          </uni-collapse>
        </view>
        <view v-show="current === 1">
          <uni-card v-if="processDetail.length > 0">
            <view class="processInfo">
              <view class="bg">
                <view class="steps">
                  <view
                    class="steps_item"
                    v-for="(i, processIndex) in processDetail"
                    :key="processIndex"
                  >
                    <view class="s_r">
                      <view class="s_r_content" style="margin-top: 10rpx">
                        <view
                          class="index"
                          :style="{
                            backgroundColor:
                              processIndex >= processDetail.length - 1
                                ? '#EAEAEA'
                                : '#0e4ae0',
                          }"
                        >
                        </view>
                        <view
                          class="line"
                          v-show="processIndex != processDetail.length - 1"
                          style="margin-top: 8rpx"
                          :style="{
                            backgroundColor:
                              processIndex >= processDetail.length - 1
                                ? '#EAEAEA'
                                : '#0e4ae0',
                          }"
                        >
                        </view>
                      </view>
                    </view>
                    <view class="s_l">
                      <view class="info_item">
                        <view class="top_info">
                          <!-- <view class="title">{{getProcessDictName(i.status)}}</view> -->
                          <view class="title">{{ i.statusName }}</view>
                          <view class="date">{{ i.createTime }}</view>
                        </view>
                        <view class="info">
                          <template>
                            <view class="text-grey" style="color: #000">
                              处理人：{{ i.handlePerson }}
                              <text class="ml5"></text>
                            </view>
                            <view class="text-grey">
                              {{ i.mark }} <text class="ml5"></text>
                            </view>
                          </template>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </uni-card>
        </view>
      </scroll-view>
      <view class="bottom-button" v-if="statusName == '待派单'">
        <button
          @click="toggle('bottom')"
          class="mini-btn"
          type="primary"
          size="mini"
          style="
            width: 100%;
            height: 68rpx;
            text-align: center;
            line-height: 68rpx;
          "
        >
          派单
        </button>
      </view>

      <view class="popupInfo">
        <uni-popup
          ref="popup"
          type="bottom"
          background-color="rgba(0,0,0,0);"
          style="z-index: 9999"
        >
          <view
            class=""
            style="
              background-color: #fff;
              border-top-left-radius: 30rpx;
              border-top-right-radius: 30rpx;
            "
          >
            <view class="" style="padding: 20rpx 20rpx">
              <view
                class=""
                style="
                  margin-left: 20rpx;
                  display: flex;
                  flex-direction: row;
                  justify-content: space-between;
                "
              >
                <view class="popup-title-style"> 分配维保人员 </view>
                <view class="popup-title-icon">
                  <uni-icons
                    type="closeempty"
                    size="30"
                    @click="close"
                  ></uni-icons>
                </view>
              </view>
            </view>
            <view
              class=""
              style="
                margin-top: 40rpx;
                width: 100%;
                height: 100%;
                padding: 0 20rpx;
              "
            >
              <view class=""> 添加维保执行人员 </view>
              <view class="search-title">
                <uni-search-bar
                  class="searchBar"
                  :focus="false"
                  v-model="inspectionPeople"
                  clearButton="auto"
                  cancelButton="none"
                  placeholder="请输入姓名"
                >
                </uni-search-bar>
                <button
                  @click="searchData"
                  size="default"
                  type="default"
                  style="
                    display: flex;
                    line-height: 68rpx;
                    font-size: 30rpx;
                    width: 136rpx;
                    height: 68rpx;
                    text-align: center;
                    background-color: #e7edfc;
                    color: #0e4ae0;
                    bordercolor: #1aad19;
                  "
                >
                  搜索
                </button>
              </view>
              <view class="popup-content">
                <radio-group @change="checkboxChange">
                  <label
                    class="uni-list-cell uni-list-cell-pd"
                    v-for="(item, index) in humanInfo"
                    :key="item.value"
                    style="
                      display: flex;
                      flex-direction: row;
                      justify-content: space-between;
                      align-items: center;
                      margin-top: 8rpx;
                      text-align: left;
                      border-bottom: 2rpx solid #f3f3f3;
                      height: 60rpx;
                    "
                  >
                    <view
                      style="
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      <radio
                        :value="JSON.stringify(item)"
                        :checked="item.checked"
                        activeBackgroundColor="#0052d9"
                        iconColor="#ffffff"
                      />
                      <text style="margin-left: 20rpx">{{
                        item.nickname
                      }}</text>
                    </view>
                    <view>{{ item.mobile }}</view>
                  </label>
                </radio-group>
              </view>
              <view class="popup-bottom-button">
                <button
                  @click="close"
                  class="mini-btn"
                  type="primary"
                  size="mini"
                  style="
                    background-color: #e7edfc;
                    color: #0e4ae0;
                    width: 320rpx;
                  "
                >
                  取消
                </button>
                <button
                  class="mini-btn"
                  type="primary"
                  size="mini"
                  @click="assignmentButton"
                  style="margin-left: 20rpx; width: 320rpx"
                >
                  确定
                </button>
              </view>
            </view>
          </view>
        </uni-popup>
      </view>

      <view class="popupInfo">
        <uni-popup
          ref="popupCharge"
          type="bottom"
          background-color="r@gba(0,0,0,0)"
        >
          <view
            class=""
            style="
              background-color: #fff;
              border-top-left-radius: 30rpx;
              border-top-right-radius: 30rpx;
            "
          >
            <view class="" style="padding: 20rpx 20rpx">
              <view
                class=""
                style="
                  margin-left: 20rpx;
                  display: flex;
                  flex-direction: row;
                  justify-content: space-between;
                "
              >
                <view class="popup-title-style"> 退回至负责人 </view>
                <view class="popup-title-icon">
                  <uni-icons
                    type="closeempty"
                    size="30"
                    @click="closeCharge"
                  ></uni-icons>
                </view>
              </view>
            </view>
            <view class="" style="margin-top: 4rpx; width: 100%; height: 100%">
              <view
                class=""
                style="
                  padding: 0 40rpx;
                  display: flex;
                  flex-direction: column;
                  justify-content: left;
                  align-items: left;
                "
              >
                <view
                  class=""
                  style="
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-start;
                    align-items: left;
                    margin-top: 8rpx;
                  "
                >
                  <view class="" style="width: 160rpx; color: #818181">
                    任务名称：
                  </view>
                  <view class="">
                    {{ workOrderName }}
                  </view>
                </view>
                <view
                  class=""
                  style="
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-start;
                    align-items: left;
                    margin-top: 8rpx;
                  "
                >
                  <view class="" style="width: 160rpx; color: #818181">
                    任务编号：
                  </view>
                  <view class="">
                    {{ workOrderDetail.workOrderCode }}
                  </view>
                </view>
                <view
                  class=""
                  style="
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-start;
                    align-items: left;
                    margin-top: 8rpx;
                  "
                >
                  <view class="" style="width: 160rpx; color: #818181">
                    负责人：
                  </view>
                  <view class="">
                    {{ workOrderDetail.headPersonName }}
                  </view>
                </view>
                <view
                  class=""
                  style="
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    align-items: left;
                    margin-top: 8rpx;
                  "
                >
                  <view class="" style="width: 160rpx; color: #818181">
                    关闭原因：
                  </view>
                  <view class="" style="margin-top: 8rpx">
                    <uni-easyinput
                      type="textarea"
                      autoHeight
                      v-model="otherDescribe"
                      placeholder="请输入内容"
                      maxlength="200"
                    ></uni-easyinput>
                  </view>
                </view>
              </view>
              <view class="popup-bottom-button">
                <button
                  @click="closeCharge"
                  class="mini-btn"
                  type="primary"
                  size="mini"
                  style="
                    background-color: #e7edfc;
                    color: #0e4ae0;
                    width: 320rpx;
                  "
                >
                  取消
                </button>
                <button
                  class="mini-btn"
                  type="primary"
                  size="mini"
                  style="margin-left: 20rpx; width: 320rpx"
                  @click="returnCharge"
                >
                  确定
                </button>
              </view>
            </view>
          </view>
        </uni-popup>
      </view>
    </view>
  </view>
</template>

<script>
  import {
    getWorkOrderDetail,
    getWorkOrderInspect,
    assignment,
    getWorkOrderFlow,
    returnHeadPerson,
    againAssignment,
    beoverdueExecution,
    startExecution,
  } from "@/api/inspectionDetail/inspectionDetail.js";
  // 提交动作
  import { submitDevice } from "@/api/inspectionExecute/inspectionExecute.js";
  import { getInspectionTaskStatusDict } from "@/api/system/dict.js";
  import { getUserRoleIdListByRoleId } from "@/api/system/user.js";
  export default {
    onLoad: function (option) {
      console.log("itemDetail", option);
      // getWorkOrderDetail(option.id).then(res => {
      // 	if (res.code == 0) {
      // 		this.workOrderDetail = res.data
      // 	}
      // })

      // getWorkOrderInspect(option.id).then(res => {
      // 	if (res.code == 0) {
      // 		this.workOrderInspect = res.data
      // 		console.log(res.data)
      // 	}
      // })

      // getWorkOrderFlow(option.id).then(res => {
      // 	if (res.code == 0) {
      // 		this.processDetail = res.data
      // 		console.log(this.processDetail)
      // 	}
      // })

      // getInspectionTaskStatusDict('Inspection_task_status').then(res => {
      // 	if (res.code == 0) {
      // 		this.inspectionDict = res.data
      // 	}
      // })

      // // 巡检流程状态字典
      // getInspectionTaskStatusDict('Inspection_process_status').then(res => {
      // 	if (res.code == 0) {
      // 		this.inspectionProcessDict = res.data
      // 	}
      // })

      this.workOrderId = "1";
      this.statusName = option.statusName;
      this.currentStatus = "1";
      this.workOrderName = "工单1";
      this.permissionIdentification = option.permissionIdentification;
      this.prePageUrl =
        "/pages/maintain/maintainManage/maintainManageDetail/maintainManageDetail";
    },
    onShow() {},
    data() {
      return {
        permissionIdentification: "",
        currentStatus: "",
        workOrderName: "",
        prePageUrl: "",
        messageText: "",
        msgType: "",
        assignmentpeople: {},
        inspectionPeople: "",
        workOrderId: 0,
        workOrderInspect: [
          {
            equipmentLocation: "WB-001",
            workOrderInspectVOList: [
              {
                name: "具体位置",
                value: "12栋101",
                status: 0,
              },
              {
                name: "设备型号",
                value: "CNC-500",
                status: 0,
              },
              {
                name: "设备类型",
                value: "空调",
                status: 0,
              },
              {
                name: "品牌",
                value: "大金",
                status: 0,
              },
              {
                name: "维保项目",
                value: "2",
                status: 1,
              },
            ],
          },
          {
            equipmentLocation: "WB-002",
            workOrderInspectVOList: [
              {
                name: "具体位置",
                value: "12栋101",
                status: 0,
              },
              {
                name: "设备型号",
                value: "CNC-500",
                status: 0,
              },
              {
                name: "设备类型",
                value: "风机",
                status: 0,
              },
              {
                name: "品牌",
                value: "大金",
                status: 0,
              },
              {
                name: "维保项目",
                value: "2",
                status: 1,
              },
            ],
          },
          {
            equipmentLocation: "WB-003",
            workOrderInspectVOList: [
              {
                name: "具体位置",
                value: "12栋101",
                status: 0,
              },
              {
                name: "设备型号",
                value: "CNC-500",
                status: 0,
              },
              {
                name: "设备类型",
                value: "新风机组",
                status: 0,
              },
              {
                name: "品牌",
                value: "大金",
                status: 0,
              },
              {
                name: "维保项目",
                value: "2",
                status: 1,
              },
            ],
          },
        ],
        inspectionDict: [],
        inspectionProcessDict: [],
        statusName: "",
        workOrderDetail: {
          frequencyTime: "08:00-12:00",
          deadline: "2025-01-15",
          executorName: "张三",
          headPersonName: "李四",
          inspectRequire: "完成所有设备的查看工作",
          completeTime: "2025-01-15",
        },
        fenge: "",
        isFollowUp: false,
        item: {
          status: "待派单",
        },
        items: ["维保明细", "任务流程图"],
        current: 0,
        otherDescribe: "",
        processDetail: [
          {
            statusName: "创建计划",
            handlePerson: "李跳跳",
            mark: "",
            createTime: "2025-01-01 12:00",
          },
          {
            statusName: "创建任务",
            handlePerson: "系统执行",
            mark: "",
            createTime: "2025-01-01 12:00",
          },
          {
            statusName: "分配维保人",
            handlePerson: "刘某",
            mark: "",
            createTime: "2025-01-01 12:00",
          },
          {
            statusName: "任务退回",
            handlePerson: "张三",
            mark: "退回原因：时间冲突",
            createTime: "2025-01-01 12:00",
          },
          {
            statusName: "分配维保人",
            handlePerson: "张三",
            mark: "",
            createTime: "2025-01-01 12:00",
          },
        ],
        humanInfo: [
          {
            nickname: "张三",
            mobile: "13357711111",
          },
          {
            nickname: "李四",
            mobile: "13357711112",
          },
          {
            nickname: "王五",
            mobile: "13357711113",
          },
        ],
      };
    },
    methods: {
      onClickItem(e) {
        if (this.current != e.currentIndex) {
          this.current = e.currentIndex;
        }
      },
      toggle() {
        getUserRoleIdListByRoleId("").then((res) => {
          if (res.code == 0) {
            this.humanInfo = res.data;
          }
        });
        this.$refs.popup.open("bottom");
      },
      toggleCharge() {
        this.$refs.popupCharge.open("bottom");
      },
      close() {
        this.$refs.popup.close("bottom");
      },
      closeCharge() {
        this.$refs.popupCharge.close("bottom");
      },
      checkboxChange(e) {
        this.assignmentpeople = JSON.parse(e.detail.value);
      },
      contentDetail(floorItem) {
        var item = floorItem;
        this.$set(item, "workOrderId", this.workOrderId);
        this.$set(item, "statusName", this.statusName);
        this.$set(item, "status", this.currentStatus);
        this.$set(
          item,
          "permissionIdentification",
          this.permissionIdentification
        );
        this.$set(item, "workOrderName", this.workOrderName);
        uni.navigateTo({
          url:
            "/pages/maintain/maintainManage/maintainItemProject/maintainItemProject?statusName=" +
            this.statusName,

          // 	url: '/pages/maintain/maintainManage/maintainItemProject/maintainItemProject?item=' +
          // encodeURIComponent(JSON.stringify(
          // 	floorItem))

          // 	url: '/pages/maintain/maintainManage/maintainItenDetail/maintainItenDetail?item=' +
          // encodeURIComponent(JSON.stringify(
          // 	floorItem))
        });
        // if ((this.statusName == '处理中') && (this.permissionIdentification ==
        // 		'mobile-menu-myInspection')) {
        // 	uni.navigateTo({
        // 		url: '/pages/inspectionExecute/index?item=' + encodeURIComponent(JSON.stringify(
        // 			floorItem))
        // 	})
        // } else {
        // 	uni.navigateTo({
        // 		url: '/pages/taskDetail/index?item=' + encodeURIComponent(JSON.stringify(floorItem))
        // 	})
        // }

        // if (!floorItem.isComplete) {

        // } else {
        // 	console.log('yyyy')

        // }
      },
      changeFollowUp() {
        this.isFollowUp = !this.isFollowUp;
      },
      getDictName(value) {
        var dictName = "";
        this.inspectionDict.forEach((item, index) => {
          if (item.value == value) {
            dictName = item.label;
            return item.label;
          }
        });
        return dictName;
      },
      getProcessDictName(value) {
        var dictName = "";
        this.inspectionProcessDict.forEach((item, index) => {
          if (item.value == value) {
            dictName = item.label;
            return item.label;
          }
        });
        return dictName;
      },
      searchData() {
        getUserRoleIdListByRoleId(this.inspectionPeople).then((res) => {
          if (res.code == 0) {
            this.humanInfo = res.data;
          }
        });
      },
      assignmentButton(type) {
        if (
          this.assignmentpeople.id == "" ||
          this.assignmentpeople.id == undefined
        ) {
          // this.msgType = type
          // this.$refs.alertDialog.open()
          uni.showToast({
            title: `请选择维保人员`,
            icon: "none",
          });
        } else {
          // 派单
          if (this.statusName == "派单") {
            assignment(
              this.workOrderId,
              this.assignmentpeople.id,
              this.assignmentpeople.nickname
            ).then((res) => {
              uni.showToast({
                title: `派单成功`,
                icon: "none",
              });
              uni.navigateTo({
                url: this.prePageUrl,
              });
            });
          }

          // 重新派单
          if (this.statusName == "已退回") {
            againAssignment(
              this.workOrderId,
              this.assignmentpeople.id,
              this.assignmentpeople.nickname
            ).then((res) => {
              uni.showToast({
                title: `重新派单成功`,
                icon: "none",
              });
              setTimeout(() => {
                uni.navigateTo({
                  url: this.prePageUrl,
                });
              }, 2000);
            });
          }
        }
      },
      returnCharge() {
        // console.log('退回到负责人',this.workOrderId)
        // console.log('退回到负责人',this.otherDescribe)
        returnHeadPerson(this.workOrderId, this.otherDescribe).then((res) => {
          uni.showToast({
            title: `退回到负责人成功`,
            icon: "none",
          });
          setTimeout(() => {
            uni.navigateTo({
              url: this.prePageUrl,
            });
          }, 2000); // 两秒后返回上一页面
        });
      },
      // 执行巡检
      executeInspection() {
        // 提交动作
        submitDevice(this.workOrderId).then((res) => {
          if (res.code == 0) {
            uni.navigateTo({
              url: "/pages/inspectionSuccess/index",
            });
          }
        });
      },
      //  开始执行
      executeStatus() {
        startExecution(this.workOrderId).then((res) => {
          if (res.code == 0) {
            uni.reLaunch({
              url:
                "/pages/inspectionDetail/index?id=" +
                this.workOrderId +
                "&statusName=" +
                this.statusName +
                "&workOrderName=" +
                this.workOrderName +
                "&status=" +
                this.currentStatus +
                "&permissionIdentification=" +
                this.permissionIdentification,
            });
          }
        });
      },
      // 逾期执行
      beoverdueExecution() {
        beoverdueExecution(this.workOrderId).then((res) => {
          if (res.code == 0) {
            uni.reLaunch({
              url:
                "/pages/inspectionDetail/index?id=" +
                this.workOrderId +
                "&statusName=" +
                this.statusName +
                "&workOrderName=" +
                this.workOrderName +
                "&status=" +
                this.currentStatus +
                "&permissionIdentification=" +
                this.permissionIdentification,
            });
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  page {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background-color: #fff;
    min-height: 100%;
    height: auto;
    background-color: #e9e9e9;

    // background-image: url(@/static/images/home/<USER>
  }

  // /deep/ .uni-popup__error[data-v-38167fe2] {
  // 	background-color: #fde2e2;
  // 	// margin-top: 100px;
  // 	z-index: 9999 !important;
  // }
  // /deep/ .uni-popup__warn[data-v-38167fe2] {
  // 	background-color: #fde2e2;
  // 	margin-top: 100px;
  // 	z-index: 9999 !important;
  // }

  /deep/ .segmented-control__text {
    font-size: 32rpx;
  }

  .totalInfo {
    padding: 0 32rpx 32rpx 32rpx;
    width: 100%;
    height: 30vh;
    position: fixed;
    background-image: url(@/static/images/home/<USER>
    background-size: cover;
    z-index: 9999 !important;
  }

  .basicInfo {
    width: 100%;
  }

  .basicInfoDetail {
    display: flex;
    flex-direction: row;
    justify-content: left;
    align-items: center;

    .basicInfoTitle {
      color: rgba(0, 0, 0, 0.4);
      min-width: 160rpx;
    }

    .basicInfoContent {
      text-align: left;
      height: 100%;
      color: #1a1a1a;
    }
  }

  .defaultHuman {
    /deep/ .uni-radio-input {
      width: 36rpx;
      height: 36rpx;
    }
  }

  .basicInfoBottom {
    background-color: #fff9f0;
    height: 60rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ff9c00;
  }

  .detailInfo {
    padding: 8rpx;
    margin-top: 33vh;
    // height: 100%;
  }

  .slot-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 60rpx;
    padding: 10rpx;
    background-color: #f6f8fe;
  }

  .slot-content-content {
    // padding: 10px;
    display: flex;
    justify-content: space-between;
    padding: 20rpx;
    background-color: #ffffff;
    border-bottom: 2rpx solid #f3f3f3;
    height: 100%;
  }

  .slot-content-content-content {
    display: flex;
    justify-content: space-between;
    height: 100%;
    width: 100%;
  }

  .content {
    /deep/ .uni-icons {
      color: #272728 !important;
    }
  }

  /deep/ .uni-collapse-item__wrap.is--transition {
    background-color: #f6f8fe;
    padding: 0 20rpx;
  }

  /deep/ .uni-collapse-item__title {
    display: flex;
    width: 100%;
    box-sizing: border-box;
    flex-direction: row;
    align-items: center;
    transition: border-bottom-color 0.3s;
    background-color: #f6f8fe;
  }

  // /deep/ .slot-content-content {
  // 	padding: 10px;
  // 	height: 100%;
  // 	overflow-y: auto;
  // }

  .bottom-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    height: 60rpx;
    margin-top: 20rpx;
  }

  .processInfo {
    // background: rgba(14, 74, 224, 0.04);
    // padding: 10px;
  }

  .popup-bottom-button {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 80rpx;
    margin-top: 20rpx;
  }

  /deep/.uni-popup .uni-popup__wrapper {
    display: block;
    position: relative;
    // height: 500px;
    // padding: 4px 4px 0px 4px;
  }

  .popup-title {
    display: flex;
    flex-direction: row;
    height: 50rpx;
    justify-content: space-between;
    padding: 10rpx;
  }

  .popup-title-style {
    font-size: 40rpx;
    font-weight: 550;
    width: 100%;
    text-align: center;
  }

  .popup-content {
    height: 500rpx;
    // background-color: #eee;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    /deep/ uni-radio .uni-radio-input {
      border-radius: 80rpx !important;
      width: 40rpx;
      height: 40rpx;
    }

    /deep/ .uni-radio-input.uni-radio-input-checked {
      background-color: #007aff !important;
      border-color: #007aff !important;
      background-clip: content-box !important;
      padding: 7rpx !important;
      box-sizing: border-box;

      &:before {
        // display: none !important;
      }
    }
  }

  .search-title {
    margin-top: 30rpx;
    display: flex;
    height: 80rpx;
    width: 100%;
    align-items: center;
    justify-content: center;
    padding: 0 20rpx 0 0;
  }

  .searchBar {
    width: 100%;
  }
</style>

<style lang="scss">
  .bg {
    width: 100%;
  }

  .steps {
    display: flex;
    flex-direction: column;

    .steps_item {
      display: flex;
      flex-direction: row;
      margin-top: 10rpx;

      .s_r {
        padding: 0 8rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 60rpx;

        // margin-top: 15px;
        .s_r_content {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 60rpx;

          .line {
            flex: 1;
            width: 5rpx;
            // border-left: 4rpx solid #0e4ae0;
          }

          .index {
            width: 24rpx;
            height: 24rpx;
            border-radius: 50rpx;
            border: 4rpx solid #e3eeff;
            box-sizing: border-box;
          }
        }
      }

      .s_l {
        display: flex;
        flex-direction: column;
        // padding: 10rpx 0;
        flex: 1;
        width: 100%;
        // width: 180rpx;

        .info_item {
          background-color: #ffffff;
          // margin-right: 10rpx;
          border-radius: 10rpx;
          display: flex;
          flex-direction: column;
          justify-content: center;
          // padding: 10rpx 0;
          width: 100%;

          .top_info {
            display: flex;
            flex-direction: row;
            align-items: center;
            flex-wrap: nowrap;
            height: 40rpx;
            justify-content: space-between;
            width: 100%;
            // justify-content: space-between;

            .date {
              // width: 140px;
            }
          }

          text {
            font-size: 24rpx;
            font-weight: 500;
            color: #0e4ae0;
          }

          .title {
            // width: calc(100vw - 330rpx);
            font-size: 28rpx;
            font-weight: 500;
            color: #0e4ae0;
            // color: rgba(102, 102, 102, 1);
            // overflow: hidden;
            // text-overflow: ellipsis;
            // display: -webkit-box;
            // flex-direction: column;
          }

          .info {
            font-size: 24rpx;
            color: #afb4be;
            margin-top: 10rpx;
          }

          .date {
            font-size: 23rpx;
            color: #afb4be;
          }

          .audit-status {
            float: right;
            width: 120rpx;
            height: 40rpx;
            line-height: 40rpx;
            text-align: center;
            font-size: 22rpx;
            background: #eafff8;
            border-radius: 20rpx;
          }
        }

        .info_item:active {
          background-color: #f4f4f4;
        }
      }
    }
  }

  .ml5 {
    margin-left: 10rpx;
  }

  .mt10 {
    margin-top: 20rpx;
  }
</style>
