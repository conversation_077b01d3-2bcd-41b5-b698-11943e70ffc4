<template>
	<view class="">
		<topInfo title="维保项目" :url="prePageUrl" :isShowBack="true" backtype="navigate" animation="true"></topInfo>
		<view class="detailInfo">
			<scroll-view class="content" scroll-y="" style="height: 89vh;">
				<view style="padding: 0 0rpx;" v-for="(floorItem,floorIndex) in workOrderInspectVOList">
					<uni-card :is-shadow="false">
						<view class="uni-body" style="display: flex;flex-direction: row;justify-content: space-between;"
							@click="contentDetail">
							<view class="">
								{{floorItem.name}}
							</view>
							<view class="" style="display: flex;flex-direction: row;">
								<view v-if="floorItem.status==1" class="" style="color: #0079fe;">
									{{floorItem.value}}
								</view>
								<view v-if="floorItem.status==0" class="" style="color:red">
									{{floorItem.value}}
								</view>
								<view class="">
									<uni-icons type="right" size="20"></uni-icons>
								</view>

							</view>
						</view>
					</uni-card>
					<!-- 					<uni-collapse ref="collapse" v-for="(item,index) in workOrderInspect" style="margin-top: 30rpx;"
						accordion>
						<uni-collapse-item titleBorder="none" :open="true">
							<template v-slot:title>
								<view class="slot-content">
									<view class="" style="border-left:8rpx #0e4ae0 solid;">
										<text style="margin-left: 20rpx;">{{item.equipmentLocation}}</text>
									</view>
								</view>
							</template>
							<view class="slot-content-content"
								v-for="(floorItem,floorIndex) in item.workOrderInspectVOList ">
								<view class="slot-content-content-content" @click="contentDetail(floorItem)">
									<view class="">
										<text style="margin-left: 20rpx;">{{floorItem.name}}</text>
									</view>
									<view class="">
										<text v-if="floorItem.status == 0"
											style="margin-left: 20rpx;">{{floorItem.value}}</text>
										<text v-if="floorItem.status == 1"
											style="margin-left: 20rpx;color:#0079fe">{{floorItem.value}}</text>
									</view>
								</view>
							</view>
						</uni-collapse-item>
					</uni-collapse> -->
				</view>
			</scroll-view>
		</view>
	</view>
	</view>
</template>

<script>
	export default {
		onLoad: function(option) {

			// console.log('mainraitemDetail', option)
			// let param = JSON.parse(decodeURIComponent(option.item));
			// console.log('mainraitemDetail',param)
			this.workOrderId = '1'
			this.statusName = option.statusName
			this.currentStatus = '1'
			this.workOrderName = '工单1'
			this.permissionIdentification = option.permissionIdentification
			this.prePageUrl = '/pages/maintain/maintainManage/itemDetail/itemDetail?statusName=' + option.statusName
		},
		onShow() {},
		data() {
			return {
				permissionIdentification: '',
				currentStatus: '',
				workOrderName: '',
				prePageUrl: '',
				messageText: '',
				msgType: '',
				assignmentpeople: {},
				inspectionPeople: '',
				workOrderId: 0,
				workOrderInspectVOList: [{
					id: 1,
					name: '维保项目：润滑油更换',
					value: '未完成',
					status: 0
				}, {
					id: 2,
					name: '维保项目：空气滤芯清洁',
					value: '已完成',
					status: 1
				}],
				inspectionDict: [],
				inspectionProcessDict: [],
				statusName: '',
				workOrderDetail: {
					frequencyTime: '08:00-12:00',
					deadline: '2025-01-15',
					executorName: '张三',
					headPersonName: '李四',
					inspectRequire: '完成所有设备的查看工作',
					completeTime: '2025-01-15'
				},
				fenge: '',
				isFollowUp: false,
				item: {
					status: '待派单'
				},
				current: 0,
				otherDescribe: '',
			}
		},
		methods: {
			contentDetail(floorItem) {
				var item = floorItem
				this.$set(item, 'workOrderId', this.workOrderId)
				this.$set(item, 'statusName', this.statusName)
				this.$set(item, 'status', this.currentStatus)
				this.$set(item, 'permissionIdentification', this.permissionIdentification)
				this.$set(item, 'workOrderName', this.workOrderName)
				uni.navigateTo({
					url: '/pages/maintain/maintainManage/maintainItenDetail/maintainItenDetail?item=' +
						encodeURIComponent(JSON.stringify(
							floorItem))
				})

			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		background-color: #fff;
		min-height: 100%;
		height: auto;
		background-color: #e9e9e9;

		// background-image: url(@/static/images/home/<USER>
	}

	// /deep/ .uni-popup__error[data-v-38167fe2] {
	// 	background-color: #fde2e2;
	// 	// margin-top: 100px;
	// 	z-index: 9999 !important;
	// }
	// /deep/ .uni-popup__warn[data-v-38167fe2] {
	// 	background-color: #fde2e2;
	// 	margin-top: 100px;
	// 	z-index: 9999 !important;
	// }

	/deep/ .segmented-control__text {
		font-size: 32rpx;
	}

	.totalInfo {
		padding: 0 32rpx 32rpx 32rpx;
		width: 100%;
		height: 30vh;
		position: fixed;
		background-image: url(@/static/images/home/<USER>
		background-size: cover;
		z-index: 9999 !important;
	}

	.basicInfo {
		width: 100%;
	}

	.basicInfoDetail {
		display: flex;
		flex-direction: row;
		justify-content: left;
		align-items: center;

		.basicInfoTitle {
			color: rgba(0, 0, 0, 0.4);
			min-width: 160rpx;
		}

		.basicInfoContent {
			text-align: left;
			height: 100%;
			color: #1a1a1a;
		}
	}

	.defaultHuman {
		/deep/ .uni-radio-input {
			width: 36rpx;
			height: 36rpx;
		}
	}

	.basicInfoBottom {
		background-color: #fff9f0;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #ff9c00;
	}

	.detailInfo {
		padding: 8rpx;
		height: 80vh;
		// margin-top: 33vh;
		// height: 100%;
	}

	.slot-content {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		height: 60rpx;
		padding: 10rpx;
		background-color: #f6f8fe;
	}

	.slot-content-content {
		// padding: 10px;
		display: flex;
		justify-content: space-between;
		padding: 20rpx;
		background-color: #ffffff;
		border-bottom: 2rpx solid #f3f3f3;
		height: 100%;
	}

	.slot-content-content-content {
		display: flex;
		justify-content: space-between;
		height: 100%;
		width: 100%;
	}

	.content {
		/deep/ .uni-icons {
			color: #272728 !important;
		}
	}


	/deep/ .uni-collapse-item__wrap.is--transition {
		background-color: #f6f8fe;
		padding: 0 20rpx;
	}

	/deep/ .uni-collapse-item__title {
		display: flex;
		width: 100%;
		box-sizing: border-box;
		flex-direction: row;
		align-items: center;
		transition: border-bottom-color .3s;
		background-color: #f6f8fe;
	}

	// /deep/ .slot-content-content {
	// 	padding: 10px;
	// 	height: 100%;
	// 	overflow-y: auto;
	// }

	.bottom-button {
		display: flex;
		flex-direction: row;
		justify-content: center;
		height: 60rpx;
		margin-top: 20rpx;
	}

	.processInfo {
		// background: rgba(14, 74, 224, 0.04);
		// padding: 10px;
	}

	.popup-bottom-button {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		height: 80rpx;
		margin-top: 20rpx;
	}

	/deep/.uni-popup .uni-popup__wrapper {
		display: block;
		position: relative;
		// height: 500px;
		// padding: 4px 4px 0px 4px;
	}

	.popup-title {
		display: flex;
		flex-direction: row;
		height: 50rpx;
		justify-content: space-between;
		padding: 10rpx;



	}

	.popup-title-style {
		font-size: 40rpx;
		font-weight: 550;
		width: 100%;
		text-align: center;
	}

	.popup-content {
		height: 500rpx;
		// background-color: #eee;
		padding: 20rpx;
		display: flex;
		flex-direction: column;
		overflow-y: auto;

		/deep/ uni-radio .uni-radio-input {
			border-radius: 80rpx !important;
			width: 40rpx;
			height: 40rpx;
		}

		/deep/ .uni-radio-input.uni-radio-input-checked {
			background-color: #007aff !important;
			border-color: #007aff !important;
			background-clip: content-box !important;
			padding: 7rpx !important;
			box-sizing: border-box;

			&:before {
				// display: none !important;
			}
		}
	}

	.search-title {
		margin-top: 30rpx;
		display: flex;
		height: 80rpx;
		width: 100%;
		align-items: center;
		justify-content: center;
		padding: 0 20rpx 0 0;

	}

	.searchBar {
		width: 100%;
	}
</style>

<style lang="scss">
	.bg {
		width: 100%;
	}

	.steps {
		display: flex;
		flex-direction: column;

		.steps_item {
			display: flex;
			flex-direction: row;
			margin-top: 10rpx;

			.s_r {
				padding: 0 8rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 60rpx;

				// margin-top: 15px;
				.s_r_content {
					height: 100%;
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 60rpx;

					.line {
						flex: 1;
						width: 5rpx;
						// border-left: 4rpx solid #0e4ae0;
					}

					.index {
						width: 24rpx;
						height: 24rpx;
						border-radius: 50rpx;
						border: 4rpx solid #e3eeff;
						box-sizing: border-box;
					}
				}
			}



			.s_l {
				display: flex;
				flex-direction: column;
				// padding: 10rpx 0;
				flex: 1;
				width: 100%;
				// width: 180rpx;

				.info_item {
					background-color: #ffffff;
					// margin-right: 10rpx;
					border-radius: 10rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					// padding: 10rpx 0;
					width: 100%;

					.top_info {
						display: flex;
						flex-direction: row;
						align-items: center;
						flex-wrap: nowrap;
						height: 40rpx;
						justify-content: space-between;
						width: 100%;
						// justify-content: space-between;

						.date {
							// width: 140px;
						}
					}

					text {
						font-size: 24rpx;
						font-weight: 500;
						color: #0e4ae0;
					}

					.title {
						// width: calc(100vw - 330rpx);
						font-size: 28rpx;
						font-weight: 500;
						color: #0e4ae0;
						// color: rgba(102, 102, 102, 1);
						// overflow: hidden;
						// text-overflow: ellipsis;
						// display: -webkit-box;
						// flex-direction: column;
					}

					.info {
						font-size: 24rpx;
						color: #afb4be;
						margin-top: 10rpx;
					}

					.date {
						font-size: 23rpx;
						color: #afb4be;
					}

					.audit-status {
						float: right;
						width: 120rpx;
						height: 40rpx;
						line-height: 40rpx;
						text-align: center;
						font-size: 22rpx;
						background: #eafff8;
						border-radius: 20rpx;
					}
				}

				.info_item:active {
					background-color: #f4f4f4;
				}
			}
		}
	}

	.ml5 {
		margin-left: 10rpx;
	}

	.mt10 {
		margin-top: 20rpx;
	}
</style>