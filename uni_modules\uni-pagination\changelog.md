## 1.2.1（2021-11-22）
- 修复 vue3中某些scss变量无法找到的问题
## 1.2.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-pagination](https://uniapp.dcloud.io/component/uniui/uni-pagination)
## 1.1.2（2021-10-08）
- 修复 current 、value 属性未监听，导致高亮样式失效的 bug
## 1.1.1（2021-08-20）
- 新增 支持国际化
## 1.1.0（2021-07-30）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.0.7（2021-05-12）
- 新增 组件示例地址
## 1.0.6（2021-04-12）
- 新增 PC 和 移动端适配不同的 ui 
## 1.0.5（2021-02-05）
- 优化 组件引用关系，通过uni_modules引用组件

## 1.0.4（2021-02-05）
- 调整为uni_modules目录规范
