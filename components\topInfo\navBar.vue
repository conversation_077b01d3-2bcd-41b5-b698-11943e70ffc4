<template>
  <view
    class="nav-bar"
    :style="{
      paddingTop: `${statusBarHeightRpx + 20}rpx`,
      height: `${statusBarHeightRpx + 88}rpx`,
    }"
  >
    <view class="nav-left">
      <uni-icons type="left" size="20" color="#333333" @click="backFn" />
    </view>
    <view class="nav-title">{{ title }}</view>
    <view class="nav-right"
      ><text @tap="toOther">{{ subTitle }}</text></view
    >
  </view>
</template>
<script>
  export default {
    props: {
      title: {
        type: String,
        default: "标题",
        require: true,
      },
      subTitle: {
        type: String,
        default: "",
      },
      url: {
        type: String,
        default: "",
        require: true,
      },
    },
    data() {
      return {
        statusBarHeightRpx: 0,
      };
    },
    mounted() {
      const systemInfo = uni.getSystemInfoSync();
      const screenWidth = systemInfo.screenWidth;
      const statusBarHeightPx = systemInfo.statusBarHeight;
      this.statusBarHeightRpx = (750 / screenWidth) * statusBarHeightPx;
      console.log(this.statusBarHeightRpx);
    },
    methods: {
      backFn() {
        uni.navigateBack();
      },
      toOther() {
        this.$tab.navigateTo(this.url);
      },
    },
  };
</script>
<style lang="scss">
  .nav-bar {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    color: white;
    font-size: 16px;
    padding: 20rpx 30rpx;
  }

  .nav-left {
    width: 33%;
    height: 44rpx;
    display: flex;
    align-items: center;
  }

  .nav-title {
    flex: 1;
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    color: #333333;
  }

  .nav-right {
    width: 33%;
    font-size: 14px;
    color: #3a7cff;
    text-align: right;
  }
</style>
