<template>
  <view class="page">
    <topInfo
      title="维修验收"
      :isShowBack="false"
      backtype="navigateBack"
      :isDark="true"
      backgroundColor="rgba(1.0,1.0,1.0,0.0)"
      :isShadow="false"
    ></topInfo>
    <uni-card class="card" title="验收结果">
      <u-radio-group v-model="resultRadiovalue" placement="column">
        <u-radio
          :customStyle="{ marginBottom: '8px' }"
          v-for="(item, index) in radiolist"
          :key="index"
          :label="item.label"
          :name="item.value"
        >
        </u-radio>
      </u-radio-group>
    </uni-card>
    <uni-card class="card" title="" v-if="resultRadiovalue == 0">
      <u-list class="rate">
        <u-list-item v-for="(item, index) in rateList" :key="index">
          <view class="rateItem">
            <view class="rateLeft">{{ item.name }}</view>
            <u-rate
              :count="item.rateCount"
              v-model="item.rateValue"
              active-color="#FF9C00"
              inactive-color="#DCDCDC"
              class="rateContent"
            ></u-rate>
            <view class="rateRight">{{ getRateName(item.rateValue) }}</view>
          </view>
        </u-list-item>
      </u-list>
    </uni-card>
    <uni-card class="card" title="工单处理" v-else>
      <u-radio-group v-model="processingRadiovalue" placement="column">
        <u-radio
          :customStyle="{ marginBottom: '8px' }"
          v-for="(item, index) in processingRadiolist"
          :key="index"
          :label="item.label"
          :name="item.value"
        >
        </u-radio>
      </u-radio-group>
    </uni-card>
    <uni-card class="card" title="服务评价" v-if="resultRadiovalue == 0">
      <u--textarea
        style="margin-top: 5px"
        v-model="evaluateDesc"
        placeholder="请输入服务评价"
        inputAlign="right"
        count
        maxlength="30"
      ></u--textarea>
    </uni-card>
    <uni-card class="card" title="说明" v-else>
      <u--textarea
        style="margin-top: 5px"
        v-model="explainDesc"
        placeholder="请输入说明"
        inputAlign="right"
        count
        maxlength="30"
      ></u--textarea>
    </uni-card>
    <u-tabbar :fixed="true" :placeholder="true" :safeAreaInsetBottom="true">
      <u-button
        color="#F3F3F3"
        type="info"
        style="height: 70rpx; margin: 6px 10px"
        @click="cancel"
      >
        <span style="color: #000">取消</span>
      </u-button>
      <u-button
        color="#0E4AE0"
        type="primary"
        text="提交"
        style="height: 70rpx; margin: 6px 10px"
        @click="submit"
      ></u-button>
    </u-tabbar>
    <u-toast ref="uToast" />
  </view>
</template>

<script>
  import {
    repairAgainAssignmentWorker,
    againaRepair,
    createEvaluate,
    confirmCompleteBehavior,
  } from "@/api/reportRepair/index.js";
  export default {
    data() {
      return {
        workOrderId: "",
        resultRadiovalue: 0,
        radiolist: [
          {
            label: "确认完成",
            value: 0,
            disabled: false,
          },
          {
            label: "未完成，仍需维修",
            value: 1,
            disabled: false,
          },
        ],
        processingRadiovalue: 0,
        processingRadiolist: [
          {
            label: "当前维修人员继续维修",
            value: 0,
            disabled: false,
          },
          {
            label: "更换维修人员重新维修",
            value: 1,
            disabled: false,
          },
        ],
        rateList: [
          {
            name: "响应速度",
            value: "responseSpeed",
            rateCount: 5,
            rateValue: 0,
            rate: "一般",
          },
          {
            name: "服务态度",
            value: "serviceEvaluation",
            rateCount: 5,
            rateValue: 0,
            rate: "比较好",
          },
          {
            name: "专业水平",
            value: "professionalLevel",
            rateCount: 5,
            rateValue: 0,
            rate: "比较好",
          },
          {
            name: "问题解决",
            value: "problemSolving",
            rateCount: 5,
            rateValue: 0,
            rate: "比较好",
          },
        ],
        evaluateDesc: "",
        explainDesc: "",
      };
    },
    methods: {
      async submit() {
        let code = undefined;
        if (this.resultRadiovalue == 0) {
          const rateObj = this.rateList.reduce((pre, cur) => {
            pre[cur.value] = cur.rateValue;
            return pre;
          }, {});
          const params = {
            workOrderId: this.workOrderId,
            ...rateObj,
            supplementary: this.evaluateDesc,
          };
          // 验收完成
          const res = await createEvaluate(params);
          if (!res.code) {
            const res = await confirmCompleteBehavior({
              id: this.workOrderId,
            });
            code = res.code;
          }
        } else {
          // 验收失败
          if (this.processingRadiovalue == 0) {
            // 重新维修
            const res = await againaRepair({
              workOrderId: this.workOrderId,
              supplement: this.explainDesc,
            });
            code = res.code;
          } else {
            // 重新派单维修
            const res = await repairAgainAssignmentWorker({
              workOrderId: this.workOrderId,
              supplement: this.explainDesc,
            });
            code = res.code;
          }
        }
        if (code === 0) {
          this.$refs.uToast.show({
            message: "提交成功",
            type: "success",
            complete: () => {
              //   uni.navigateTo({
              //     url: "/pages/maintenance/pendingOrders/index?type=报修记录&status=88&workType=1",
              //   });
              uni.navigateBack({
                delta: 2,
              });
            },
          });
        } else {
          this.$refs.uToast.show({
            message: "提交失败",
            type: "error",
          });
        }
      },
      cancel() {
        uni.navigateBack();
      },
      getRateName(rateValue) {
        let rateName = "";
        switch (rateValue) {
          case 1:
            rateName = "比较差";
            break;
          case 2:
            rateName = "差";
            break;
          case 3:
            rateName = "一般";
            break;
          case 4:
            rateName = "好";
            break;
          case 5:
            rateName = "特别好";
            break;
        }
        return rateName;
      },
    },
    mounted() {},
    onLoad(option) {
      this.workOrderId = option.workOrderId;
    },
  };
</script>

<style scoped lang="scss">
  .page {
    height: 1200rpx;
    background-image: url(@/static/images/home/<USER>
    background-size: contain;
    /* 背景图等比缩放充整个容器 */
    background-position: center;

    /* 背景图居中对齐 */
    .u-radio {
      height: 60rpx;
    }

    .u-radio__text {
      font-family: MiSans, MiSans !important;
      font-weight: 400 !important;
      font-size: 28rpx !important;
      color: rgba(0, 0, 0, 0.9) !important;
      line-height: 96rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .content {
    width: 100%;
    height: 100%;

    .content-item {
      margin-top: 20rpx;
      display: flex;
      justify-content: space-between;
      width: 100%;
      font-family: MiSans, MiSans;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.9);
      line-height: 30rpx;
      text-align: right;
      font-style: normal;
      text-transform: none;
      border-bottom: 1rpx solid #e7e7e7;
      padding-bottom: 20rpx;

      .item-left {
        // width: 40%;
      }

      .item-right {
        width: 75%;
        // padding-left: 30rpx;
        height: 100%;

        img {
          vertical-align: bottom;
          margin-right: 5rpx;
        }
      }
    }

    :nth-last-child(1) {
      border: 0;
    }
  }

  .rate {
    height: 320rpx !important;

    .rateItem {
      display: flex;
      justify-content: space-between;
      height: 80rpx;
      line-height: 80rpx;
      border-bottom: 1px solid #e7e7e7;

      .rateLeft {
        width: 25%;
        font-family: MiSans, MiSans;
        font-weight: 400;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.6);
        line-height: 80rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .rateContent {
        width: 55%;
      }

      .rateRight {
        width: 20%;
        font-family: MiSans, MiSans;
        font-weight: 400;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.9);
        line-height: 80rpx;
        text-align: right;
        font-style: normal;
        text-transform: none;
      }
    }
  }
</style>
