<template>
	<view class="work-container">
		<topInfo title="执行巡检" :url="prePageUrl" :isShowBack="true" backtype="navigate"></topInfo>
		<!-- <topInfo title="任务详情" :url="prePageUrl" :isShowBack="true" backtype="navigate"></topInfo> -->
		<view class="tttt" style="z-index:9999;width: 100%;height: 100%;float: right;">
			<button @click="toggle('bottom')" class="mini-btn" type="primary" size="mini"
				style="background-color: #e7edfc;color:#1349e0;width: 80px;z-index:9999;float: right;right:5px;top: 8px;position: fixed;">保存</button>
		</view>
		<view class="" style="padding: 10px;">
			<u-tabs style="flex: 0 0 100rpx;" :list="deviceDetailInfo" :is-scroll="true" :current="currentTag"
				@change="changeTags"></u-tabs>
			<view class="" style="height: 76vh;">
				<scroll-view scroll-y="true" style="height: 100%;">
					<view class="" style="margin-top: 10px;">
						<uni-card type="line" is-full>
							<template v-slot:title>
								<view class=""
									style="display: flex;flex-direction: row;justify-content: space-between;border-bottom: 1px solid #f2f2f2;line-height: 40px;">
									<view class="" style="display: flex;justify-content: center;align-items: center;">
										<view class="" style="width: 10px;height: 20px;;border-left: 4px solid #0e4ae0;">
											&nbsp;
										</view>
										<view class="" style="font-size: 16px;font-weight: 550;margin-left: 4px;">设备信息
										</view>
									</view>
								</view>
							</template>
							<view class="basicInfo">
								<view class="basicInfoDetail">
									<view class="basicInfoTitle">设备名称:</view>
									<view class="basicInfoContent">{{deviceInfo.name}}</view>
								</view>
								<view class="basicInfoDetail">
									<view class="basicInfoTitle">设备编号:</view>
									<view class="basicInfoContent">{{deviceInfo.equipmentCode}}</view>
								</view>
								<view class="basicInfoDetail">
									<view class="basicInfoTitle">设备类型:</view>
									<view class="basicInfoContent">{{deviceInfo.deviceTypeName}}</view>
								</view>
								<view class="basicInfoDetail">
									<view class="basicInfoTitle">存放位置:</view>
									<view class="basicInfoContent">{{deviceInfo.location}}</view>
								</view>
							</view>
						</uni-card>
					</view>
				<!-- 	<view class="completeInfo" style="margin-top: 10px;">
						<uni-card type="line" is-full>
							<template v-slot:title>
								<view class=""
									style="display: flex;flex-direction: row;border-bottom: 1px solid #f2f2f2;line-height: 40px;">
									<view class="" style="display: flex;justify-content: center;align-items: center;">
										<view class=""
											style="width: 10px;height: 20px;;border-left: 4px solid #0e4ae0;">
											&nbsp;
										</view>
										<view class=""
											style="font-size: 16px;font-weight: 550;height: 40px;margin-left: 4px;">
											检查项</view>
									</view>
								</view>
							</template>
							<view class="basicInfo">
								<view class="basicInfoDetailCheck" v-for="(item,index) in inspectDetailList" :key=""
									style="margin-top: 4px;">
									<view class="checkBasicInfoDetail" style="padding: 4px 0;">
										<view class="checkBasicInfoTitle">{{index+1}}.{{item.content}}</view>
										<view class="checkBasicInfoContent" v-show="item.inspectResultMethod == 0">
											<view class="checkBasicInfoContentTitle">巡检结果：</view>
											<view class="checkBasicInfoContentEndNormal" v-if="item.result == '0'">
												<uni-icons custom-prefix="iconfont" type="icon-dui" size="18"
													color="#0052d9"></uni-icons>
												正常
											</view>
											<view class="checkBasicInfoContentEndAbnormal" v-if="item.result == '1'">
												<uni-icons custom-prefix="iconfont" type="icon-gantanhao" size="14"
													color="#f52f3e"></uni-icons>
												异常
											</view>
										</view>
										<view class="checkBasicInfoContent" v-show="item.inspectResultMethod == 1">
											<view class="checkBasicInfoContentTitle">巡检结果：</view>
											<view class="checkBasicInfoContentEndNormal" v-if="item.result == '0'">
												<uni-icons custom-prefix="iconfont" type="icon-dui" size="18"
													color="#0052d9"></uni-icons>
												<view style="margin-left: 6px;">{{item.numericalInformationResult}}
												</view>
											</view>
											<view class="checkBasicInfoContentEndAbnormal" v-if="item.result == '1'"
												style="margin-left: 4px;">
												<uni-icons custom-prefix="iconfont" type="icon-gantanhao" size="14"
													color="#f52f3e"></uni-icons>
												<view style="margin-left: 6px;">{{item.numericalInformationResult}}
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</uni-card>
					</view> -->
					<view class="" style="margin-top: 10px;">
						<uni-card type="line" is-full>
							<template v-slot:title>
								<view class=""
									style="display: flex;flex-direction: row;justify-content: space-between;border-bottom: 1px solid #f2f2f2;line-height: 40px;">
									<view class="" style="display: flex;justify-content: center;align-items: center;">
										<view class="" style="width: 10px;height: 20px;;border-left: 4px solid #0e4ae0;">
											&nbsp;
										</view>
										<view class="" style="font-size: 16px;font-weight: 550;margin-left: 4px;">检查项
										</view>
									</view>
								</view>
							</template>
							<view class=" checkProject">
								<view class="checkProjectDetail" v-for="(item,index) in inspectDetailList">
									<view class="">{{index+1}}.{{item.content}}</view>
									<view class="" v-if="item.inspectResultMethod== '0'">
										<radio-group @change="radioChange"
											style="display: flex;flex-direction: row;justify-content: left;">
											<view class=""><label class="uni-list-cell uni-list-cell-pd">
													<radio value="0" :checked="radioChange1 == 0"
														style="transform:scale(0.7);" />正常
												</label></view>
											<view class=""><label class="uni-list-cell uni-list-cell-pd">
													<radio value="1" :checked="radioChange1 == 1"
														style="transform:scale(0.7)" />异常
												</label></view>
										</radio-group>
									</view>
									<view class="" style="display: flex;flex-direction: row;" v-if="item.inspectResultMethod== '1'">
										<uni-easyinput class="uni-mt-5" v-model="set2" placeholder="请输入数值"
											@iconClick="iconClick"></uni-easyinput>
										<view class=""
											style="text-align: center;display: flex;justify-content: center;align-items: center; width: 20px;">
											{{item.numericalInformationJson?getUnitDictName(item.numericalInformationJson.unit):''}}
										</view>
									</view>
								</view>
							</view>
						</uni-card>
					</view>
					<view class="" style="margin-top: 10px;">
						<uni-card type="line" is-full>
							<template v-slot:title>
								<view class=""
									style="display: flex;flex-direction: row;justify-content: space-between;border-bottom: 1px solid #f2f2f2;line-height: 40px;">
									<view class="" style="display: flex;justify-content: center;align-items: center;">
										<view class="" style="width: 10px;height: 20px;;border-left: 4px solid #0e4ae0;">
											&nbsp;
										</view>
										<view class="" style="font-size: 16px;font-weight: 550;margin-left: 4px;">图片上传
										</view>
									</view>
								</view>
							</template>
							<view class=" basicInfo">
								<view class="example-body">
									<uni-file-picker limit="6" title="最多选择6张图片" v-model="imageValue"
										fileMediatype="image" mode="grid" @select="select" @progress="progress"
										@success="successUploadFile" @fail="fail"></uni-file-picker>
								</view>
							</view>
						</uni-card>
					</view>
				</scroll-view>
			</view>
			<view class=""
				style="display: flex;flex-direction: row;justify-content: space-between;margin-top: 20px;height: 30px;">
				<button @click="upChange" class="mini-btn" type="primary" size="mini" style="width: 160px;" :disabled="currentTag - 1 < 0">上一个</button>
				<button @click="doneChange" class="mini-btn" type="primary" size="mini"
					style="margin-left: 10px;width: 160px;" :disabled="currentTag + 1 >= deviceDetailInfo.length">下一个</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getWorkOrderInspect
	} from '@/api/inspectionDetail/inspectionDetail.js'
	import {
		getDeviceClassfy,
		submitDevice,
		updateWorkDetail
	} from '@/api/inspectionExecute/inspectionExecute.js'
	import {
		getEquipmentInspectDetailByHeadPerson
	} from '@/api/taskDetail/taskDetail.js'
	import {
		getInspectionTaskStatusDict
	} from '@/api/system/dict.js'
	export default {
		onLoad(option) {
			// 获取设备详细信息
			getWorkOrderInspect(option.workOrderId).then(res => {
				if (res.code == 0) {
					this.workOrderInspect = res.data
					if (this.workOrderInspect.length > 0) {
						for (var i = 0; i < this.workOrderInspect.length; i++) {
							var location = this.workOrderInspect[i].equipmentLocation
							var deviceInfo = this.workOrderInspect[i].workOrderInspectVOList
							for (var j = 0; j < deviceInfo.length; j++) {
								this.$set(deviceInfo[j], 'name', deviceInfo[j].equipmentName)
								this.$set(deviceInfo[j], 'location', location)
								this.deviceDetailInfo.push(deviceInfo[j])
							}
						}
						this.deviceInfo = this.deviceDetailInfo[0]
					}
					// 获得设备类型
					getDeviceClassfy(this.deviceInfo.equipmentType).then(res => {
						if (res.code == 0) {
							var deviceTypeName = res.data?res.data.name:''
							this.$set(this.deviceInfo, 'deviceTypeName', deviceTypeName)
						}
					})
					
					getEquipmentInspectDetailByHeadPerson(option.workOrderId, this.deviceInfo.id).then(res => {
						if (res.code == 0) {
							this.inspectDetailList = res.data.inspectDetailList
							console.log('this.inspectDetailList',this.inspectDetailList)
						}
					})
				}
			})		
			
			getInspectionTaskStatusDict('data_unit').then(res=>{
				if(res.code == 0){
					this.unitDict = res.data
				}
			})
			this.workOrderId = option.workOrderId

			this.prePageUrl = '/pages/inspectionDetail/index?statusName=' + option.type + '&currentStatus=' + option
				.status +
				'&permissionIdentification=' + option.permissionIdentification + '&workOrderName=' + option.workOrderName +
				'&id=' + option.workOrderId
		},
		data() {
			return {
				currentTag: 0,
				workOrderId:'',
				// 单位字典
				unitDict:[],
				// 设备详细信息
				inspectDetailList:[],
				deviceInfo: {},
				deviceDetailInfo: [],
				workOrderInspect: [],
				// 前一个页面url
				prePageUrl: '',
				fengge: '',
				set2: '',
				set4: '',
				maxCharacters: 200, // 最多字符数
				charCount: 0, // 当前字符数
				currentContent: '',
				value: [],
				imageValue: [],
				// 校验规则
				rules: {
					name: {
						rules: [{
							required: true,
							errorMessage: '姓名不能为空'
						}]
					},
					age: {
						rules: [{
							required: true,
							errorMessage: '年龄不能为空'
						}, {
							format: 'number',
							errorMessage: '年龄只能输入数字'
						}]
					}
				},
				// 校验表单数据
				valiFormData: {
					name: '',
					age: '',
					introduction: '',
				},
				// 自定义表单校验规则
				customRules: {
					name: {
						rules: [{
							required: true,
							errorMessage: '姓名不能为空'
						}]
					},
					age: {
						rules: [{
							required: true,
							errorMessage: '年龄不能为空'
						}]
					},
					hobby: {
						rules: [{
							format: 'array'
						}]
					}

				},
				dynamicFormData: {
					email: '',
					domains: {}
				},
				otherDescribe: '',
				radioChange1: '0',
				radioChange2: '0',
			}
		},
		onReady() {
			// 设置自定义表单校验规则，必须在节点渲染完毕后执行
			// this.$refs.customForm.setRules(this.customRules)
		},
		methods: {
			updateCharCount() {
				console.log(this.$data.otherDescribe)
				this.charCount = this.$data.otherDescribe.length + 1;
			},
			changeTags(item) {
				// console.log(item)
				this.deviceInfo = item
				getDeviceClassfy(item.equipmentType).then(res => {
					if (res.code == 0) {
						var deviceTypeName = res.data?res.data.name:''
						this.$set(this.deviceInfo, 'deviceTypeName',deviceTypeName)
					}
				})
				getEquipmentInspectDetailByHeadPerson(this.workOrderId, this.deviceInfo.id).then(res => {
					if (res.code == 0) {
						this.inspectDetailList = res.data.inspectDetailList
					}
				})
				this.currentTag = item.index;
			},
			// 获取上传状态
			select(e) {
				console.log('选择文件：', e)
				const filePath = e.tempFilePaths[0];
				const file = e.tempFiles[0];
				//获取图片临时路径
					uni.uploadFile({
						url: 'http://*************:48080/admin-api/infra/file/upload', //【必填】图片上传地址
						filePath, //【必填】（files和filePath选其一）要上传文件资源的路径。
						name: 'file', //【必填】上传名字，注意与后台接收的参数名一致
						header: this.headers, //设置请求头
						//请求成功，后台返回自己服务器上的图片地址
						success: d => {
							d = JSON.parse(d.data)
							console.log(d)
							// 上传成功了
							if (d.success) {
								let response = d;
								if (response.data && response.data.key) {
									// 下载失败原因的描述文件
									this.$d.customer_downloadImportCustomerExcel({
										key: response.data.key
									}, {
										s: (d) => {
											this.$g.downloadFile(d, `${file.name}-上传失败原因`, '.xls');
											uni.showToast({
												icon: `none`,
												title: `${file.name}-上传失败，请查看失败原因`
											});
											setTimeout(() => {
												// this.initList(true); //刷新列表
											}, 1500);
											//console.log('上传失败', response, file, fileList);
										}
									});
								} else if (response.success) {
									// 上传成功了
									uni.showToast({
										icon: `none`,
										title: `“${file.name}”导入成功`
									});
									setTimeout(() => {
										// this.initList(true); //刷新列表
									}, 1500);
									//console.log('上传成功', response, file, fileList);
								} else {
									// 其他失败原因
									uni.showToast({
										icon: `none`,
										title: response.msg
									});
									//console.log('上传失败', response, file, fileList);
								}
							} else {
								uni.showToast({
									icon: `none`,
									title: d.msg
								});
							}
						}
					});
				// const file = e.
			},
			// 获取上传进度
			progress(e) {
				console.log('上传进度：', e)
			},

			// 上传成功
			successUploadFile(e) {
				console.log('上传成功', e)
			},

			// 上传失败
			fail(e) {
				console.log('上传失败：', e)
			},
			input(e) {
				console.log('输入内容：', e);
			},
			iconClick(type) {
				uni.showToast({
					title: `点击了${type==='prefix'?'左侧':'右侧'}的图标`,
					icon: 'none'
				})
			},
			radioChange: function(evt) {
				this.$data.radioChange1 = evt.detail.value
				this.$data.radioChange2 = evt.detail.value
				// console.log(evt)
				// for (let i = 0; i < this.items.length; i++) {
				// 	if (this.items[i].value === evt.detail.value) {
				// 		this.current = i;
				// 		break;
				// 	}
				// }
			},
			upChange() {
				if (this.currentTag - 1 >= 0) {
					this.currentTag = this.currentTag - 1
					this.deviceInfo = this.deviceDetailInfo[this.currentTag]
					getDeviceClassfy(this.deviceInfo.equipmentType).then(res => {
						if (res.code == 0) {
							var deviceTypeName = res.data?res.data.name:''
							this.$set(this.deviceInfo, 'deviceTypeName',deviceTypeName)
						}
					})
					getEquipmentInspectDetailByHeadPerson(this.workOrderId, this.deviceInfo.id).then(res => {
						if (res.code == 0) {
							this.inspectDetailList = res.data.inspectDetailList
						}
					})
				}
			},
			doneChange() {
				if (this.currentTag + 1 <= this.deviceDetailInfo.length) {
					this.currentTag = this.currentTag + 1
					this.deviceInfo = this.deviceDetailInfo[this.currentTag]
					getDeviceClassfy(this.deviceInfo.equipmentType).then(res => {
						if (res.code == 0) {
							var deviceTypeName = res.data?res.data.name:''
							this.$set(this.deviceInfo, 'deviceTypeName',deviceTypeName)
						}
					})
					getEquipmentInspectDetailByHeadPerson(this.workOrderId, this.deviceInfo.id).then(res => {
						if (res.code == 0) {
							this.inspectDetailList = res.data.inspectDetailList
						}
					})
					// this.deviceInfo = item
				}
			},
			getUnitDictName(value) {
				var dictName = ''
				this.unitDict.forEach((item, index) => {
					if (item.value == value) {
						dictName = item.label
						return item.label
					}
				})
				return dictName
			},
		}
	}
</script>

<style lang="scss">
	page {
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		background-color: #fff;
		min-height: 100%;
		height: auto;
		background-color: #f2f2f2;
		overflow-y: hidden;
		// padding: 10px;
	}

	.basicInfo {
		width: 100%;
	}

	.basicInfoDetail {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;

		.basicInfoTitle {
			// color: rgba(0, 0, 0, 0.4);
			color: #000;
			min-width: 80px;
		}

		.basicInfoContent {
			color: rgba(0, 0, 0, 0.4);
			text-align: left;
			height: 100%;
		}
	}

	.container {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}

	.char-count {
		margin-top: -22px;
		font-size: 12px;
		margin-right: 4px;
		color: #888;
		z-index: 9999;
	}
</style>