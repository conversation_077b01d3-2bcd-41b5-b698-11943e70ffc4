<template>
	<view class="work-container">
		<topInfo title="维保详情" :url="prePageUrl" :isShowBack="true" backtype="navigate" animation="true"></topInfo>
		<view class="totalInfo" style="width: 100%; height: 540rpx;padding: 20rpx;">
			<scroll-view style="height: 90vh;">
				<view class="" style="margin-top: 20rpx;">
					<uni-card type="line" is-full>
						<template v-slot:title>
							<view class=""
								style="display: flex;flex-direction: row;border-bottom: 2rpx solid #f2f2f2;line-height: 80rpx;">
								<view class="" style="display: flex;justify-content: center;align-items: center;">
									<view class="" style="width: 20rpx;height: 40rpx;;border-left: 8rpx solid #0e4ae0;">
										&nbsp;
									</view>
									<view class=""
										style="font-size: 32rpx;font-weight: 550;height: 80rpx;margin-left: 8rpx;">
										维保详情</view>
								</view>
							</view>
						</template>
						<view class="basicInfo">
							<view class="basicInfoDetail">
								<view class="basicInfoTitle">维保项目名称:</view>
								<view class="basicInfoContent">空调维保</view>
							</view>
							<view class="basicInfoDetail">
								<view class="basicInfoTitle">维保项目编号:</view>
								<view class="basicInfoContent">WB-001</view>
							</view>
							<view class="basicInfoDetail">
								<view class="basicInfoTitle">维保部位:</view>
								<view class="basicInfoContent">滤芯清洁</view>
							</view>
							<view class="basicInfoDetail">
								<view class="basicInfoTitle">维保设备名称:</view>
								<view class="basicInfoContent">大金001</view>
							</view>
							<view class="basicInfoDetail">
								<view class="basicInfoTitle">维保级别:</view>
								<view class="basicInfoContent">普通</view>
							</view>
							<view class="basicInfoDetail" style="height: 10vh;">
								<view class="basicInfoTitle">维保要求:</view>
								<scroll-view class="basicInfoContent" scroll-y=""
									style="max-height: 10vh;width: 100%;text-align: right;">1.需要仔细检查滤芯是否损坏 <br>
									2.更新滤芯时检查工作是否正常</scroll-view>
							</view>
						</view>
					</uni-card>
				</view>
				<view class="bottom-button" style="margin-top: 10vh;">
					<button v-if="completeStatus== '未完成'" @click="goToMyMaintainDetailAdd(false)" class="mini-btn" type="primary"
						size="mini"
						style="width: 100%;height: 68rpx;text-align: center;line-height: 68rpx;">去维保</button>
					<button v-if="completeStatus== '已完成'" @click="goToMyMaintainDetailAdd(true)" class="mini-btn" type="primary"
						size="mini"
						style="width: 100%;height: 68rpx;text-align: center;line-height: 68rpx;">查看维保结果</button>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		getEquipmentInspectDetailByHeadPerson
	} from '@/api/taskDetail/taskDetail.js'
	import demo1 from '../../../../static/demo/demo1.png'
	import demo2 from '../../../../static/demo/demo2.png'
	import demo3 from '../../../../static/demo/demo3.png'
	import demo4 from '../../../../static/demo/demo4.png'
	export default {
		onLoad(option) {
			const item = JSON.parse(decodeURIComponent(option.item));
			this.completeStatus = item.value
			// console.log(item)
			// getEquipmentInspectDetailByHeadPerson(item.workOrderId, item.id).then(res => {
			// 	if (res.code == 0) {
			// 		this.inspectDetailList = res.data.inspectDetailList
			// 		this.photoUrls = res.data.photoUrls
			// 		console.log(this.inspectDetailList)
			// 	}
			// })
			// this.basicInfo = item
			this.permissionIdentification = option.permissionIdentification
			this.prePageUrl = '/pages/maintain/myMaintainDetail/myMaintainDetailThree/myMaintainDetailThree?statusName'+item.statusName
		},
		data() {
			return {
				completeStatus: '',
				permissionIdentification: '',
				imageValue: [{
					url: demo1
				}, {
					url: demo2
				}, {
					url: demo3
				}, {
					url: demo4
				}],
				prePageUrl: '',
				basicInfo: {},
				inspectDetailList: [],
				photoUrls: [],
				fengge: '',
				set2: '',
				set4: '',
				maxCharacters: 200, // 最多字符数
				charCount: 0, // 当前字符数
				current: 0,
				currentContent: '',
				value: [],
				// 校验规则
				rules: {
					name: {
						rules: [{
							required: true,
							errorMessage: '姓名不能为空'
						}]
					},
					age: {
						rules: [{
							required: true,
							errorMessage: '年龄不能为空'
						}, {
							format: 'number',
							errorMessage: '年龄只能输入数字'
						}]
					}
				},
				// 校验表单数据
				valiFormData: {
					name: '',
					age: '',
					introduction: '',
				},
				// 自定义表单校验规则
				customRules: {
					name: {
						rules: [{
							required: true,
							errorMessage: '姓名不能为空'
						}]
					},
					age: {
						rules: [{
							required: true,
							errorMessage: '年龄不能为空'
						}]
					},
					hobby: {
						rules: [{
							format: 'array'
						}]
					}

				},
				dynamicFormData: {
					email: '',
					domains: {}
				},
				otherDescribe: '',
				radioChange1: '0',
				radioChange2: '0',
			}
		},
		onReady() {
			// 设置自定义表单校验规则，必须在节点渲染完毕后执行
			// this.$refs.customForm.setRules(this.customRules)
		},
		methods: {
			updateCharCount() {
				console.log(this.$data.otherDescribe)
				this.charCount = this.$data.otherDescribe.length + 1;
			},
			change(index) {
				// console.log(index)
				this.$data.current = index.index;
			},

			// 上传失败
			fail(e) {
				console.log('上传失败：', e)
			},
			input(e) {
				console.log('输入内容：', e);
			},
			iconClick(type) {
				uni.showToast({
					title: `点击了${type==='prefix'?'左侧':'右侧'}的图标`,
					icon: 'none'
				})
			},
			previewImage(item) {
				// console.log('previewImage')
				uni.previewImage({
					urls: [item]
				})
			},
			goToMyMaintainDetailAdd(status){
				uni.navigateTo({
					url:'/pages/maintain/myMaintainDetail/myMaintainDetailAdd/myMaintainDetailAdd?value='+this.completeStatus
				})
			}

		}
	}
</script>

<style lang="scss">
	page {
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		background-color: #fff;
		// min-height: 100%;
		// height: auto;
		background-color: #e9e9e9;
		overflow-y: hidden;
		// padding: 10px;
	}

	.totalInfo {
		position: fixed;
		background-image: url(@/static/images/home/<USER>
		background-size: cover;
		z-index: 9999 !important;
		height: 90vh;
	}

	.basicInfo {
		width: 100%;
		height: 100%;
	}

	.basicInfoDetail {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		border-bottom: 2rpx solid #e6e6e6;
		height: 80rpx;

		.basicInfoTitle {
			// color: rgba(0, 0, 0, 0.4);
			color: #000;
			min-width: 160rpx;
		}

		.basicInfoContent {
			color: rgba(0, 0, 0, 0.4);
			text-align: left;
			height: 100%;
			display: flex;
			align-items: center;
			flex-wrap: wrap;
		}
	}

	.basicInfoDetailCheck {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		// align-items: center;
		// border-bottom: 1px solid #e6e6e6;
		height: 100rpx;

		.basicInfoTitle {
			// color: rgba(0, 0, 0, 0.4);
			color: #000;
			min-width: 160rpx;
		}

		.basicInfoContent {
			color: rgba(0, 0, 0, 0.4);
			text-align: left;
			height: 100%;
			display: flex;
			align-items: center;
		}
	}

	.container {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}

	.char-count {
		margin-top: -44rpx;
		font-size: 24rpx;
		margin-right: 8rpx;
		color: #888;
		z-index: 9999;
	}

	.checkBasicInfoDetail {
		display: flex;
		flex-direction: column;
		border-bottom: 1px solid #e6e6e6;
		height: 110rpx;
		margin-top: 20rpx;

		.checkBasicInfoTitle {
			// color: rgba(0, 0, 0, 0.4);
			color: #000;
			min-width: 160rpx;

		}

		.checkBasicInfoContent {
			color: rgba(0, 0, 0, 0.4);
			text-align: left;
			height: 100%;
			display: flex;
			align-items: center;
			flex-direction: row;
			height: 160rpx;

			.checkBasicInfoContentTitle {
				width: 200rpx;
			}

			.checkBasicInfoContentEndNormal {
				color: #0052d9;
				display: flex;
				justify-content: left;
				align-items: flex-start;
				width: 100%;
				// margin-top: 14px;
			}

			.checkBasicInfoContentEndAbnormal {
				color: #f52f3e;
				display: flex;
				justify-content: left;
				align-items: flex-start;
				width: 100%;
			}

		}
	}

	.completeInfo {
		/deep/ .uni-card .uni-card__content {
			padding: 0 20rpx 20rpx 20rpx !important;
			font-size: 28rpx;
			color: #6a6a6a;
			line-height: 44rpx;
		}
	}

	.example-body {
		/deep/ .uni-grid--border {
			position: relative;
			z-index: 1;
			border-left: 0px #D2D2D2 solid;
		}
	}
</style>