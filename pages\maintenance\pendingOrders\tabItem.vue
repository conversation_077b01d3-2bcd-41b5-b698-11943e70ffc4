<template>
  <scroll-view class="tablItem" scroll-y @scrolltolower="scrolltolowerFn">
    <uni-card
      class="card"
      @click="cardClick(item)"
      v-for="(item, index) in pendingOrdersList"
      :key="index"
      margin="10rpx 20rpx"
    >
      <template v-slot:title>
        <view class="card-title">
          <view class="left">
            <view class="icon">
              <image
                src="@/static/images/icon.png"
                alt="工单图标"
                style="width: 40rpx; height: 40rpx; vertical-align: bottom"
              />
            </view>
            <view class="title"> 工单：{{ item.workOrderCode }} </view>
          </view>
          <view class="right">
            <u-tag
              :text="getDictName(item.status)"
              :type="getDictStyle(item.status)"
              borderColor="transparent"
              size="mini"
              plain
              plainFill
            ></u-tag>
          </view>
        </view>
      </template>
      <view class="content">
        <view class="content-item">
          <view class="item-left">报修区域：</view>
          <view class="item-right">{{ item.locationName }}</view>
        </view>
        <view class="content-item">
          <view class="item-left">报修类型：</view>
          <view class="item-right">{{
            item.reportRepairType == "0" ? "设备报修" : "其他报修"
          }}</view>
        </view>
        <view class="content-item">
          <view class="item-left">故障描述：</view>
          <view class="item-right">{{ item.faultDesc }}</view>
        </view>
      </view>
      <!-- <view class="footer">
				<view class="footer-left">
					<img src="@/static/images/clock.png" alt="" width='16px' />
				</view>
				<view class="footer-right">
					45分钟
				</view>
			</view> -->
    </uni-card>
    <view v-if="noMoreData" class="no-more">没有更多数据了</view>
  </scroll-view>
</template>

<script>
  import {
    getPendingOrdersList,
    getMyReportRepairStatistics,
  } from "@/api/reportRepair/index.js";
  export default {
    components: {},
    props: ["currentTab", "dictList", "itemPageSize", "selectValue"],
    data() {
      return {
        pendingOrdersList: [],
        pageNo: 1,
        pageSize: 10,
        total: 0,
        noMoreData: false,
      };
    },
    methods: {
      cardClick(item) {
        const pages = getCurrentPages(); // 获取当前页面栈
        const currentPage = pages[pages.length - 1]; // 当前页面实例
        const options = currentPage.options; // 当前页面的路由参数
        uni.navigateTo({
          url:
            "/pages/maintenance/OrderDetail/index" +
            "?workOrderId=" +
            item.id +
            "&workType=" +
            options.workType +
            "&status=" +
            item.status,
        });
      },
      // 获取报修列表
      async getPendingOrdersList() {
        if (this.noMoreData) return;
        const params = {
          pageNo: 1,
          pageSize: this.pageSize,
          status: this.currentTab.status,
          permissionIdentification: this.currentTab.permissionIdentification,
          workOrderCode: this.selectValue,
        };
        const {
          data: { list, total },
        } = await getPendingOrdersList(params);
        this.pendingOrdersList = list;
        this.total = total;
        if (this.pendingOrdersList.length >= this.total) {
          this.noMoreData = true;
        }
      },
      scrolltolowerFn() {
        this.getPendingOrdersList();
      },
      // 获取字典值
      getDictName(status) {
        return this.dictList.find((item) => item.value == status)?.label;
      },

      getDictStyle(status) {
        let tagStyle = this.dictList.find(
          (item) => item.value == status
        )?.colorType;
        if (tagStyle == "danger") tagStyle = "error";
        return tagStyle;
      },
    },
    onShow() {
      console.log("页面显示");
      this.getPendingOrdersList();
    },
    mounted() {
      console.log("页面加载");
      this.getPendingOrdersList();
    },
    watch: {
      itemPageSize(newVal, oldVal) {
        this.pageSize = newVal; // 重置
        this.getPendingOrdersList(); // 调用获取列表方法
      },
      selectValue(newVal, oldVal) {
        this.noMoreData = false; // 重置
        this.pageSize = 10;
        this.getPendingOrdersList(); // 调用获取列表方法
      },
    },
  };
</script>

<style lang="scss" scoped>
  .tablItem {
    padding-bottom: 20rpx;
    .no-more {
      text-align: center;
      padding: 20px;
      color: #999;
    }
  }

  .card {
    border-radius: 10rpx;
    background: #ffffff;
    box-shadow: 0px 4px 6px -1px rgba(0, 64, 152, 0.05),
      0px 0px 10px 0px rgba(0, 64, 152, 0.05);
  }

  .card-title {
    display: flex;
    justify-content: space-between;
    margin-top: 20rpx;
    margin-left: 10rpx;

    .title {
      margin-left: 10rpx;
    }

    .left {
      display: flex;
      justify-content: space-between;

      .icon {
        img {
          vertical-align: middle;
        }
      }
    }
  }

  .content {
    width: 100%;
    height: 100%;
    background: rgba(14, 74, 224, 0.04);
    border-radius: 6px 6px 6px 6px;
    padding: 20rpx;

    .content-item {
      margin-top: 10rpx;
      display: flex;
      justify-content: space-between;
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.4);
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;

      .item-left {
        width: 30%;
      }

      .item-right {
        width: 88%;
        color: rgba(0, 0, 0, 0.9);
      }
    }

    :nth-child(1) {
      margin-top: 0rpx;
    }
  }

  .footer {
    display: flex;
    float: right;
    margin-top: 15rpx;
    width: 120rpx;
    height: 50rpx;
    border-radius: 0px 0px 0px 0px;

    .footer-left {
      img {
        vertical-align: middle;
      }

      margin-right: 10rpx;
    }
  }
</style>
