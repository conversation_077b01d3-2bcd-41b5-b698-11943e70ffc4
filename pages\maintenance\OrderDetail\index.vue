<template>
  <view class="page">
    <topInfo
      title="报修详情"
      url="/pages/maintenance/pendingOrders/index"
      :goback="true"
      :isShowBack="true"
      backtype="navigate"
      :isDark="false"
      backgroundColor="#fff"
    ></topInfo>
    <uni-card class="card" :is-full="true">
      <template v-slot:title>
        <view class="card-title">
          <view class="left">
            <view class="title"> 工单：{{ baseInfo.workOrderCode }} </view>
          </view>
          <view class="right">
            <u-tag
              :text="getDictName(status)"
              :type="getDictStyle(status)"
              size="mini"
              plain
              plainFill
            ></u-tag>
          </view>
        </view>
      </template>
      <view class="content">
        <view class="content-item">
          <view class="item-left">创建人：</view>
          <view class="item-right">{{ baseInfo.userName }}</view>
        </view>
        <view class="content-item">
          <view class="item-left">创建时间：</view>
          <view class="item-right">{{ baseInfo.createTime }}</view>
        </view>
      </view>
    </uni-card>
    <u-sticky bgColor="#fff" style="margin-bottom: 20rpx">
      <tui-tabs
        :tabs="tabList"
        :currentTab="currentTab"
        @change="change"
      ></tui-tabs>
    </u-sticky>
    <tabItem :currentTab="currentTab"></tabItem>

    <!-- 退单 -->
    <u-action-sheet :show="showReturn" title="退单" @close="showReturn = false">
      <view class="returnInfo">
        <u--textarea
          v-model="returnInfo"
          placeholder="请输入退单原因"
          height="300rpx"
          style="margin: 0rpx 40rpx 20rpx; text-align: left"
          count
          maxlength="30"
        ></u--textarea>
      </view>
      <view class="returnBtns">
        <u-button
          color="#F3F3F3"
          type="info"
          style="height: 70rpx; margin: 6px 10px"
          @click="showReturn = false"
        >
          <span style="color: #000">取消</span>
        </u-button>
        <u-button
          color="#0E4AE0"
          type="primary"
          text="确定"
          style="height: 70rpx; margin: 6px 10px"
          @click="enterReturn"
        ></u-button>
      </view>
    </u-action-sheet>
    <u-loading-page :loading="returnLoading"></u-loading-page>

    <!-- 驳回 -->
    <u-action-sheet :show="showReject" title="驳回" @close="showReject = false">
      <view class="returnInfo">
        <u--textarea
          v-model="rejectInfo"
          placeholder="请输入驳回原因"
          height="300rpx"
          style="margin: 0rpx 40rpx 20rpx; text-align: left"
          count
          maxlength="30"
        ></u--textarea>
      </view>
      <view class="returnBtns">
        <u-button
          color="#F3F3F3"
          type="info"
          style="height: 70rpx; margin: 6px 10px"
          @click="showReject = false"
        >
          <span style="color: #000">取消</span>
        </u-button>
        <u-button
          color="#0E4AE0"
          type="primary"
          text="确定"
          style="height: 70rpx; margin: 6px 10px"
          @click="enterReject"
        ></u-button>
      </view>
    </u-action-sheet>
    <u-loading-page :loading="rejectLoading"></u-loading-page>

    <!-- 派单 -->
    <u-action-sheet
      :show="showDispatch"
      title="派单"
      @close="showDispatch = false"
    >
      <view class="returnInfo">
        <u--input
          placeholder="请输入姓名"
          v-model="userParams.name"
          prefixIcon="search"
          shape="circle"
          style="margin: 20rpx 40rpx 10rpx; height: 50rpx"
          prefixIconStyle="font-size: 44rpx;color: #909399"
          @blur="loadmore"
        ></u--input>
      </view>
      <u-list
        :loading="userListLoading"
        :isLoadMore="isUserListLoadMore"
        @scrolltolower="scrolltolower"
        style="height: 50vh; overflow: scroll"
        :pagingEnabled="true"
      >
        <u-radio-group v-model="userRadioValue" placement="column">
          <u-list-item
            v-for="(item, index) in userList"
            :key="index"
            @click="itemClick"
          >
            <view class="userItem">
              <u-radio :key="index" :name="item.value" :label="item.nickname">
              </u-radio>
              <view class="userItem-mobile">
                {{ item.mobile ? item.mobile : "15855351326" }}
              </view>
            </view>
          </u-list-item>
        </u-radio-group>
      </u-list>
      <view class="returnBtns">
        <u-button
          color="#F3F3F3"
          type="info"
          style="height: 70rpx; margin: 6px 10px"
          @click="showDispatch = false"
        >
          <span style="color: #000">取消</span>
        </u-button>
        <u-button
          color="#0E4AE0"
          type="primary"
          text="确定"
          style="height: 70rpx; margin: 6px 10px"
          @click="enterDispatch"
        ></u-button>
      </view>
    </u-action-sheet>
    <u-loading-page :loading="rejectLoading"></u-loading-page>

    <!-- 撤销 -->
    <u-modal
      :show="isShowRevoke"
      @confirm="revokeConfirm"
      title="撤销"
      content="确定撤销当前报修申请？"
      ref="uModal"
      :asyncClose="true"
    ></u-modal>

    <u-tabbar :fixed="true" :placeholder="true" :safeAreaInsetBottom="true">
      <!-- 普通用户 -->
      <u-button
        color="#0E4AE0"
        type="primary"
        text="确认完成"
        style="height: 70rpx; margin: 6px 10px"
        @click="completeClick"
        v-if="this.workType == '1' && [17].includes(this.status)"
      ></u-button>
      <u-button
        color="#F3F3F3"
        type="info"
        style="height: 70rpx; margin: 6px 10px"
        @click="revokeClick"
        v-if="this.workType == '1' && [6, 12, 15].includes(this.status)"
      >
        <span style="color: #000">撤回</span>
      </u-button>
      <!-- 维修人员 -->
      <u-button
        color="#F3F3F3"
        type="info"
        style="height: 70rpx; margin: 6px 10px"
        @click="returnBtn"
        v-if="this.workType == '2' && [12].includes(this.status)"
      >
        <span style="color: #000">退单</span>
      </u-button>
      <u-button
        color="#0E4AE0"
        type="primary"
        text="接单"
        style="height: 70rpx; margin: 6px 10px"
        v-if="this.workType == '2' && [12].includes(this.status)"
        @click="acceptBtn"
      ></u-button>
      <u-button
        color="#0E4AE0"
        type="primary"
        text="确认到场"
        style="height: 70rpx; margin: 6px 10px"
        @click="confirmClick"
        v-if="this.workType == '2' && [15].includes(this.status)"
      ></u-button>
      <u-button
        color="#0E4AE0"
        type="primary"
        text="完成维修"
        style="height: 70rpx; margin: 6px 10px"
        @click="completeRepairClick"
        v-if="this.workType == '2' && [16].includes(this.status)"
      ></u-button>
      <!-- 维修主管 -->
      <u-button
        color="#F3F3F3"
        type="info"
        style="height: 70rpx; margin: 6px 10px"
        @click="rejectBtn"
        v-if="this.workType == '3' && [6].includes(this.status)"
      >
        <span style="color: #000">驳回</span>
      </u-button>
      <u-button
        color="#0E4AE0"
        type="primary"
        text="派单"
        style="height: 70rpx; margin: 6px 10px"
        @click="dispatchBtn"
        v-if="this.workType == '3' && [6, 14].includes(this.status)"
      ></u-button>
    </u-tabbar>
    <u-toast ref="uToast" />
  </view>
</template>

<script>
  import tuiTabs from "@/components/thorui/tui-tabs/tui-tabs.vue";
  import tabItem from "./tabItem.vue";
  import {
    getMyReportRepairDetail,
    getMyReportRepairDict,
    returnWorkOrder,
    revokeWorkOrder,
    revokePendingOrders,
    revokeWaitBePresent,
    getUserRoleIdListByRoleId,
    repairAssignmentWorkOrder,
    rejectWorkOrder,
    acceptWorkerOrders,
    confirmComplete,
    reRepairAssignmentWorkOrder,
  } from "@/api/reportRepair/index.js";
  export default {
    components: {
      tuiTabs,
      tabItem,
    },
    data() {
      return {
        currentTab: 0,
        status: "",
        workOrderId: "",
        workType: "",
        dictList: [],
        // 退单弹框
        showReturn: false,
        returnInfo: "",
        returnLoading: false,
        // 驳回弹窗
        showReject: false,
        rejectInfo: "",
        rejectLoading: false,
        // 派单弹窗
        showDispatch: false,
        dispatchUserName: "",
        dispatchLoading: false,
        userList: [],
        userListLoading: false,
        isUserListLoadMore: true, // 是否可以加载更多数据
        userRadioValue: "",
        // 撤销
        isShowRevoke: false,
        baseInfo: {
          workOrderCode: "",
          userName: "",
          createTime: "",
        },
        tabList: [
          {
            name: "报修信息",
          },
          {
            name: "处理进度",
          },
          {
            name: "维修信息",
          },
          {
            name: "服务评价",
          },
        ],
        userParams: {
          roleId: "156",
          name: "",
        },
      };
    },
    methods: {
      change(e) {
        this.currentTab = e.index;
      },

      async getWorkOrderDetail() {
        const { data } = await getMyReportRepairDetail({
          id: this.workOrderId,
        });
        this.baseInfo = {
          workOrderCode: data.workOrderCode,
          userName: data.userName,
          createTime: data.createTime,
        };
      },
      async getMyReportDict() {
        const {
          data: { list },
        } = await getMyReportRepairDict({
          pageSize: 100,
          dictType: "repair_process_status",
        });
        this.dictList = list;
        uni.setStorageSync("myReportRepairDict", list);
      },
      // 获取字典值（报修状态）
      getDictName(status) {
        return this.dictList.find((item) => item.value == status)?.label;
      },
      getDictStyle(status) {
        let tagStyle = this.dictList.find(
          (item) => item.value == status
        )?.colorType;
        if (tagStyle == "danger") tagStyle = "error";
        return tagStyle;
      },
      // 获取字典值（流程状态）
      async getMyReportDict2() {
        const {
          data: { list },
        } = await getMyReportRepairDict({
          pageSize: 100,
          dictType: "repair_process_diagram_status",
        });
        uni.setStorageSync("processDict", list);
      },
      returnBtn() {
        this.showReturn = true;
      },
      async enterReturn() {
        if (!this.returnInfo) return;
        this.returnLoading = true;
        const { code } = await returnWorkOrder({
          workOrderId: this.workOrderId,
          supplement: this.returnInfo,
        });
        if (!code) {
          this.$refs.uToast.show({
            message: "退回成功",
            type: "success",
            complete: () => {
              uni.navigateBack();
            },
          });
        } else {
          this.$refs.uToast.show({
            message: "退回失败",
            type: "error",
          });
        }
        this.returnLoading = false;
        this.showReturn = false;
      },
      // 驳回操作
      rejectBtn() {
        this.showReject = true;
      },
      async enterReject() {
        if (!this.rejectInfo) return;
        this.rejectLoading = true;
        const { code } = await rejectWorkOrder({
          workOrderId: this.workOrderId,
          supplement: this.rejectInfo,
        });
        if (!code) {
          this.$refs.uToast.show({
            message: "驳回成功",
            type: "success",
            complete: () => {
              uni.navigateBack();
            },
          });
        } else {
          this.$refs.uToast.show({
            message: "驳回失败",
            type: "error",
          });
        }
        this.rejectLoading = false;
        this.showReject = false;
      },
      // 派单操作
      dispatchBtn() {
        this.showDispatch = true;
        this.loadmore();
      },
      scrolltolower(scrollTop) {
        this.loadmore();
      },
      async loadmore() {
        const { data } = await getUserRoleIdListByRoleId(this.userParams);
        this.userList = data.map((item) => {
          return {
            ...item,
            value: item.id,
          };
        });
        // if (this.loading || !this.isLoadMore) return;
        // this.loading = true;
        // try {
        //   // 将新数据追加到列表中
        //   const {data:{list}} = await getUserRoleIdListByRoleId(this.params)
        //   this.indexList = [...this.indexList, ...list]
        //   // 判断是否还有更多数据
        //   if (list.length < this.params.pageSize) { // 假设接口每页返回10条数据
        //     this.isLoadMore = false;
        //   } else {
        //     this.params.pageNo += 1;
        //   }
        // } catch (error) {
        //   console.error("加载数据出错：", error);
        // } finally {
        //   this.loading = false;
        // }
      },
      async enterDispatch() {
        if (!this.userRadioValue) return;
        if (this.status == 14) {
          const { code } = await reRepairAssignmentWorkOrder({
            workOrderId: this.workOrderId,
            executor: this.userRadioValue,
          });
          if (!code) {
            this.$refs.uToast.show({
              message: "派单成功",
              type: "success",
              complete: () => {
                uni.navigateBack();
              },
            });
          } else {
            this.$refs.uToast.show({
              message: "派单失败",
              type: "error",
              complete: () => {},
            });
          }
        } else {
          const { code } = await repairAssignmentWorkOrder({
            workOrderId: this.workOrderId,
            executor: this.userRadioValue,
          });
          if (!code) {
            this.$refs.uToast.show({
              message: "派单成功",
              type: "success",
              complete: () => {
                uni.navigateBack();
              },
            });
          } else {
            this.$refs.uToast.show({
              message: "派单失败",
              type: "error",
              complete: () => {},
            });
          }
        }

        this.showDispatch = false;
      },
      // 撤销操作
      revokeClick() {
        this.isShowRevoke = true;
      },
      async revokeConfirm() {
        this.isShowRevoke = false;
        let resCode = 1;
        if (this.status == 15) {
          const { code } = await revokeWaitBePresent({
            id: this.workOrderId,
          });
          resCode = code;
        }
        if (this.status == 12) {
          const { code } = await revokePendingOrders({
            id: this.workOrderId,
          });
          resCode = code;
        }
        if (!resCode) {
          this.$refs.uToast.show({
            message: "撤销成功",
            type: "success",
            complete: () => {
              this.isShowRevoke = false;
              uni.navigateBack();
            },
          });
        } else {
          this.$refs.uToast.show({
            message: "撤销失败",
            type: "error",
            complete: () => {
              this.isShowRevoke = false;
            },
          });
        }
      },
      // 确认完成
      completeClick() {
        uni.navigateTo({
          url:
            "/pages/maintenance/complete/index?workOrderId=" + this.workOrderId,
        });
      },
      // 接单
      async acceptBtn() {
        const { code } = await acceptWorkerOrders({
          id: this.workOrderId,
        });
        if (!code) {
          this.$refs.uToast.show({
            message: "接单成功",
            type: "success",
            complete: () => {
              uni.navigateBack();
            },
          });
        } else {
          this.$refs.uToast.show({
            message: "接单失败",
            type: "error",
          });
        }
      },
      // 到场确认
      confirmClick() {
        uni.navigateTo({
          url:
            "/pages/maintenance/confirm/index?workOrderId=" + this.workOrderId,
        });
      },
      completeRepairClick() {
        uni.navigateTo({
          url:
            "/pages/maintenance/completeRepair/index?workOrderId=" +
            this.workOrderId,
        });
      },
      getParams(data) {
        console.log(data);
      },
    },
    mounted() {},
    onLoad(option) {
      this.status = Number(option.status);
      this.workOrderId = option.workOrderId;
      this.workType = option.workType;
      this.getMyReportDict();
      // 获取任务详情
      this.getWorkOrderDetail();
      this.getMyReportDict2();
    },
  };
</script>

<style scoped>
  .page {
    background: rgba(0, 0, 0, 0.02);
    min-height: 100vh;
  }

  .card {
    background: #ffffff;
  }

  .card-title {
    display: flex;
    justify-content: space-between;
    margin-top: 20rpx;
    margin-left: 10rpx;

    .title {
      margin-left: 10rpx;
    }

    .left {
      display: flex;
      justify-content: space-between;

      .icon {
        img {
          vertical-align: middle;
        }
      }
    }
  }

  .content {
    width: 100%;
    height: 100%;
    background: #eff3fb;
    border-radius: 6px 6px 6px 6px;
    padding: 20rpx;

    .content-item {
      margin-top: 10rpx;
      display: flex;
      justify-content: space-between;
      width: 100%;
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.4);
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;

      .item-left {
        width: 30%;
      }

      .item-right {
        width: 80%;
        color: rgba(0, 0, 0, 0.9);
      }
    }

    :nth-child(1) {
      margin-top: 0rpx;
    }
  }

  .returnInfo {
    display: flex;
    justify-content: start;
  }

  .returnBtns {
    display: flex;
    justify-content: center;
  }

  .list-cell {
    display: flex;
    box-sizing: border-box;
    width: 100%;
    padding: 10px 24rpx;
    overflow: hidden;
    color: #323233;
    font-size: 14px;
    line-height: 24px;
    background-color: #fff;
  }

  .userItem {
    display: flex;
    justify-content: space-between;
    margin: 10rpx auto;
    width: 640rpx;
    height: 96rpx;
    border-bottom: 1px solid #e7e7e7;

    .u-radio__text {
      font-family: MiSans, MiSans !important;
      font-weight: 400 !important;
      font-size: 28rpx !important;
      color: rgba(0, 0, 0, 0.9) !important;
      line-height: 96rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    view {
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.9);
      line-height: 96rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .userItem-nickname {
      width: 70%;
    }

    .userItem-mobile {
      width: 30%;
    }
  }
</style>
