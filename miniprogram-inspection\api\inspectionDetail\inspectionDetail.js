import request from '@/utils/request'


export function getWorkOrderDetail(workOrderId) {
	const data = {
		workOrderId
	}
	return request({
		url: '/devops/app/work-order/getWorkOrderDetail',
		'method': 'GET',
		'data': data
	})
}

export function getWorkOrderInspect(workOrderId) {
	const data = {
		workOrderId
	}
	return request({
		url: '/devops/app/work-order/getWorkOrderInspect',
		'method': 'GET',
		'data': data
	})
}

// 派单
export function assignment(workOrderId,executor,executorName) {
	const data = {
		workOrderId,
		executor,
		executorName
	}
	return request({
		url: '/devops/work-order/assignment',
		'method': 'GET',
		'data': data
	})
}
// 任务流程图
export function getWorkOrderFlow(workOrderId) {
	const data = {
		id:workOrderId,
	}
	return request({
		url: '/devops/work-order-flow/get',
		'method': 'GET',
		'data': data
	})
}


// 退回到负责人
export function returnHeadPerson(id,returnReason) {
	const data = {
		id,
		returnReason
	}
	return request({
		url: '/devops/work-order/returnHeadPerson',
		'method': 'GET',
		'data': data
	})
}


// 重新派单
export function againAssignment(workOrderId,executor,executorName) {
	const data = {
		workOrderId,
		executor,
		executorName
	}
	return request({
		url: '/devops/work-order/againAssignment',
		'method': 'GET',
		'data': data
	})
}


// 开始执行
export function startExecution(workOrderId) {
	const data = {
		id:workOrderId
	}
	return request({
		url: '/devops/work-order/startExecution',
		'method': 'GET',
		'data': data
	})
}


// 逾期执行
export function beoverdueExecution(workOrderId) {
	const data = {
		id:workOrderId
	}
	return request({
		url: '/devops/work-order/beoverdueExecution',
		'method': 'GET',
		'data': data
	})
}


