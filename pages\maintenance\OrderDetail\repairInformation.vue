<template>
	<view class="tablItem">
		<uni-card class="card">
			<template v-slot:title>
				<view class="card-title">
					<view class="left">
						<view class="icon">
							<image src="@/static/images/detailIcon.png" alt="工单图标" style="width: 10rpx;height: 30rpx;vertical-align: middle;" />
						</view>
						<view class="title">
							基本信息
						</view>
					</view>
				</view>
			</template>
			<view class="content">
				<view class="content-item">
					<view class="item-left">所属部门</view>
					<view class="item-right">{{baseInfo.deptName}}</view>
				</view>
				<view class="content-item">
					<view class="item-left">联系姓名</view>
					<view class="item-right">{{baseInfo.userName}}</view>
				</view>
				<view class="content-item">
					<view class="item-left">联系电话</view>
					<view class="item-right">{{baseInfo.userPhone}}</view>
				</view>
			</view>
		</uni-card>
	
		<uni-card class="card">
			<template v-slot:title>
				<view class="card-title">
					<view class="left">
						<view class="icon">
							<image src="@/static/images/detailIcon.png" alt="工单图标" style="width: 10rpx;height: 30rpx;vertical-align: middle;" />
						</view>
						<view class="title">
							报修内容
						</view>
					</view>
				</view>
			</template>
			<view class="content">
				<view class="content-item">
					<view class="item-left">报修区域</view>
					<view class="item-right">{{baseInfo.locationName}}</view>
				</view>
				<view class="content-item">
					<view class="item-left">详细地址</view>
					<view class="item-right">{{baseInfo.detailedAddress}}</view>
				</view>
				<view class="content-item">
					<view class="item-left">报修类型</view>
					<view class="item-right">{{baseInfo.reportRepairType=='0'?'设备报修':'其他报修'}}</view>
				</view>
				<view class="content-item" v-if="baseInfo.reportRepairType=='0'">
					<view class="item-left">设备类型</view>
					<view class="item-right">{{baseInfo.equipmentType}}</view>
				</view>
				<view class="content-item" v-if="baseInfo.reportRepairType=='0'">
					<view class="item-left">设备名称</view>
					<view class="item-right">{{baseInfo.equipmentName}}</view>
				</view>
				<view class="content-item">
					<view class="item-left">故障描述</view>
					<view class="item-right">
						{{baseInfo.faultDesc}}
					</view>
				</view>
				<view class="content-item" style="text-align: left;">
					<view>
						<!-- 图片/视频 -->
						图片
						<view class="album">
							<view class="album__content">
								<u-album :urls="baseInfo.photoUrlList" multipleSize='180rpx'></u-album>
							</view>
						</view>
					</view>
				</view>
				<!-- <view class="content-item">
					<view class="item-left">报修音频</view>
					<view class="item-right">
						<view @click="voiceClick">
							<img src="@/static/images/voice.png" alt="音频" width='18rpx'/>
							36s
						</view>
					</view>
				</view> -->
			</view>
		</uni-card>
	</view>
</template>

<script>
	import {getMyReportRepairDetail,getMyReportRepairDict} from '@/api/reportRepair/index.js'
	export default {
		components: {},
		props: ['currentTab'],
		data() {
			return {
				OrdersList: [{
				}, ],
				baseInfo:{
					deptName:'',
					userName:'',
					userPhone:'',
					reportRepairType:'',
					locationName:'',
					detailedAddress:'',
					faultDesc:'',
					photoUrlList:[]
				},
				albumWidth: 0,
			}
		},
		methods: {
			voiceClick(){
			},
			async getWorkOrderDetail(){
				const pages = getCurrentPages(); // 获取页面栈
				const currentPage = pages[pages.length - 1]; // 获取当前页面实例
				const options = currentPage.options; // options 就是路由参数对象
				const {data} = await getMyReportRepairDetail({id:options.workOrderId})
				this.baseInfo = {...this.baseInfo,...data,photoUrlList:data.photoUrlList?data.photoUrlList:[]}
			},
		},
		mounted() {
			this.getWorkOrderDetail()
		}
	}
</script>


<style lang="scss">
	.tablItem {
		min-height: 100vh;
		height: 100%;
		padding-bottom: 20rpx;
	}

	.card {
		border-radius: 10rpx;
		background: #FFFFFF;
		box-shadow: 0px 4px 6px -1px rgba(0, 64, 152, 0.05), 0px 0px 10px 0px rgba(0, 64, 152, 0.05);
	}

	.card-title {
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
		margin-left: 10rpx;

		.title {
			margin-left: 10rpx;
		}

		.left {
			display: flex;
			justify-content: space-between;

			.icon {
				img {
					vertical-align: middle;
				}
			}
		}
	}

	.content {
		width: 100%;
		height: 100%;

		.content-item {
			margin-top: 30rpx;
			display: flex;
			justify-content: space-between;
			width: 100%;
			font-family: MiSans, MiSans;
			font-size: 28rpx;
			color: rgba(0, 0, 0, 0.9);
			line-height: 30rpx;
			text-align: right;
			font-style: normal;
			text-transform: none;
			border-bottom: 1rpx solid #E7E7E7;
			padding-bottom: 30rpx;

			.item-left {
				// width: 40%;
			}

			.item-right {
				width: 75%;
				// padding-left: 30rpx;
				height: 100%;
					img {
						vertical-align: bottom;
						margin-right:5rpx
					}
			}
		}

		:nth-last-child(1) {
			border: 0;
		}
	}

	.album {
		@include flex;
		align-items: flex-start;
		margin-top: 30rpx;
		width: 100%;
		&__content {
			flex: 1;
		}
	}
</style>