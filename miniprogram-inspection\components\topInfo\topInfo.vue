<template>
  <view
    class=""
    style="
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
    "
  >
    <uni-nav-bar
      v-if="isShowBack"
      :dark="isDark"
      :fixed="true"
      :shadow="isShadow"
      :background-color="backgroundColor"
      status-bar
      left-icon="left"
      :title="title"
      @clickLeft="backroute"
    />
    <uni-nav-bar
      v-if="!isShowBack"
      :dark="isDark"
      :fixed="true"
      :shadow="isShadow"
      :background-color="backgroundColor"
      status-bar
      :title="title"
    />
    <!-- 	<view v-if="isShowBack" class="" style="text-align: left;">
			<uni-icons type="back" size="30" @click="backroute"></uni-icons>
		</view>
		<view class=""
			style="text-align: center;margin-top: 3px;font-size: 18px;font-weight: 600;width: 100%;text-align: center;">
			{{title}}
		</view> -->

    <!-- <view class="topInfo" style="display: flex;flex-direction: row; justify-content: space-between;width: 100%;">
		<uni-nav-bar dark :fixed="true" shadow background-color="#d5f2fc" color="#17181a"  status-bar left-icon="left" left-text="返回"
			:title="title" @clickLeft="backroute" style="z-index: 9999 !important;" /> -->
    <!-- <uni-nav-bar dark :fixed="true" shadow background-color="transparent" color="#17181a"  status-bar left-icon="left" left-text="返回"
			:title="title" @clickLeft="backroute" style="z-index: 9999 !important;" /> -->
  </view>
</template>
<script>
  export default {
    props: {
      title: {
        type: String,
        default: "",
        require: true,
      },
      url: {
        type: String,
        default: "",
        require: true,
      },
      isShowBack: {
        type: Boolean,
        default: false,
        require: true,
      },
      backtype: {
        type: String,
        default: "switch",
        require: true,
      },
      backgroundColor: {
        type: String,
        default: "#1850e6",
      },
      isDark: {
        type: Boolean,
        default: true,
      },
      goback: {
        type: Boolean,
        default: false,
      },
      isShadow: {
        type: Boolean,
        default: true,
      },
      animation: {
        type: String,
        default: "",
      },
      urlParams: {
        type: Object,
        default: () => {
          return {};
        },
      },
    },
    methods: {
      backroute() {
        var url = this.url;
        var backType = this.backtype;
        var animation = this.animation;
        if (this.goback) {
          uni.navigateBack({
            delta: 1, // 返回的层级，1 表示返回上一层页面
            success() {
              uni.$emit("refreshPage");
            },
          });
          return;
        }
        if (backType == "switch") {
          uni.switchTab({
            url: url,
            animationType: "none",
          });
        } else if (backType == "navigate") {
          if (animation) {
            uni.navigateTo({
              url: url,
              animationType: "slide-in-left",
            });
          } else {
            uni.navigateTo({
              url: url,
            });
          }
        } else if (backType == "navigateBack") {
          uni.navigateBack({
            delta: 1,
          });
        } else if (backType == "navigateBackWithParams") {
          this.$tab.navigateBackWithParams(this.urlParams);
        }
      },
    },
  };
</script>
<style lang="scss">
  .topInfo {
    // background: linear-gradient(-90deg, #d5f2fc, #e6eefd);
    color: #ccc;
    z-index: 9999;
  }

  /deep/ .uni-nav-bar-text {
    font-size: 32rpx;
    font-weight: 500;
  }

  /deep/ .uni-navbar--border {
    border: 0 !important;
  }
</style>
