<template>
  <view class="page">
    <topInfo
      :title="pageTitle"
      :isShowBack="true"
      backtype="navigate"
      url="/pages/workOrderManager/index"
      :isDark="false"
      backgroundColor="#fff"
    >
    </topInfo>
    <view class="searchInput">
      <u--input
        placeholder="请输入报修单号"
        prefixIcon="search"
        fontSize="30rpx"
        shape="circle"
        style="height: 50rpx; background-color: #f3f3f3"
        prefixIconStyle="font-size: 44rpx;color: #909399"
        :clearable="true"
        v-model="searchValue"
        @confirm="searchFn"
        @clear="clearFn"
      ></u--input>
    </view>
    <u-sticky bgColor="#fff" style="margin-bottom: 20rpx">
      <!-- <tui-tabs :tabs="tabList" :currentTab="currentTab" @change="change"></tui-tabs> -->
      <uni-grid
        class="gridClass"
        :column="tabList.length"
        :highlight="true"
        :showBorder="false"
        borderColor="#81d3f8"
        :square="false"
      >
        <uni-grid-item
          class="gridItemClass"
          v-for="(item, index) in tabList"
          :index="index"
          :key="index"
          :style="{ color: currentTab == index ? '#0e4ae0' : '#666666' }"
        >
          <view class="grid-item-box" @click="change(item, index)">
            <view class="infoNum">{{ item.num }}</view>
            <view class="text">{{ item.name }}</view>
          </view>
        </uni-grid-item>
      </uni-grid>
    </u-sticky>
    <tabItem
      :currentTab="currentTabData"
      :key="currentTab"
      :dictList="dictList"
      :itemPageSize="pageSize"
      :selectValue="selectValue"
    ></tabItem>
  </view>
</template>

<script>
  import tuiTabs from "@/components/thorui/tui-tabs/tui-tabs.vue";
  import tabItem from "./tabItem.vue";
  import {
    getMyReportRepairStatistics,
    getMyReportRepairDict,
    getStatisticList,
  } from "@/api/reportRepair/index.js";
  export default {
    components: {
      tabItem,
      tuiTabs,
    },
    data() {
      return {
        currentTab: 0,
        currentTabData: {},
        tabList: [
          {
            name: "全部",
            num: 0,
            status: "",
          },
          {
            name: "未处理",
            num: 0,
            // status: [6, 12, 15, 89] ,// 待派单、待接单、待到场
            status: 89, // 待派单、待接单、待到场
          },
          {
            name: "处理中",
            num: 0,
            status: 16, // 处理中
          },
          {
            name: "待验收",
            num: 0,
            status: 17, // 待验收
          },
          {
            name: "已完成",
            num: 0,
            // status: [19, 13, 18, 90] ,// 已完成、已驳回、已撤回
            status: 90, // 已完成、已驳回、已撤回
          },
        ],
        list: [],
        currentData: "",
        dictList: [],
        pageSize: 10,
        searchValue: "",
        selectValue: "",
        pageTitle: "报修申请",
      };
    },
    methods: {
      change(item, index) {
        this.currentTabData = item;
        this.currentTab = index;
        this.pageSize = 10;
      },
      // 点击分类
      // changeGrid(item) {
      // 	// console.log(item)
      // 	this.currentData = item.status
      // 	this.currentCountTotal = item.countTotal
      // 	if (item.status != 0) {
      // 		this.$set(this.requestParam, 'status', item.status)
      // 	} else {
      // 		this.$set(this.requestParam, 'status', '')
      // 	}
      // 	getInspectionInfo(this.requestParam).then(res => {
      // 		if (res.code == 0) {
      // 			this.workOrder = res.data.list
      // 		}
      // 	})
      // },
      // 获取字典值
      getDictName(value) {
        var dictName = "";
        this.inspectionDict.forEach((item, index) => {
          if (item.value == value) {
            dictName = item.label;
            return item.label;
          }
        });
        return dictName;
      },
      // 设置tablist页面
      async setTabList(workType) {
        // workType: 我的报修：1，我的维修：2，维修管理：3
        if (workType == 1) {
          this.tabList = [
            {
              name: "全部",
              num: 0,
              status: "",
              permissionIdentification: "mobile-menu-myReportRepair",
            },
            {
              name: "未处理",
              num: 0,
              // status: [6, 12, 15, 89] ,// 待派单、待接单、待到场
              status: 89, // 待派单、待接单、待到场
              permissionIdentification: "mobile-menu-myReportRepair",
            },
            {
              name: "处理中",
              num: 0,
              status: 16, // 处理中
              permissionIdentification: "mobile-menu-myReportRepair",
            },
            {
              name: "待验收",
              num: 0,
              status: 17, // 待验收
              permissionIdentification: "mobile-menu-myReportRepair",
            },
            {
              name: "已完成",
              num: 0,
              // status: [19, 13, 18, 90] ,// 已完成、已驳回、已撤回
              status: 90, // 已完成、已驳回、已撤回
              permissionIdentification: "mobile-menu-myReportRepair",
            },
          ];
        } else if (workType == 2) {
          this.tabList = [
            {
              name: "全部",
              num: 0,
              status: "",
              permissionIdentification: "mobile-menu-myMaintenance",
            },
            {
              name: "待接单",
              num: 0,
              status: 12,
              permissionIdentification: "mobile-menu-myMaintenance",
            },
            {
              name: "待到场",
              num: 0,
              status: 15,
              permissionIdentification: "mobile-menu-myMaintenance",
            },
            {
              name: "处理中",
              num: 0,
              status: 16,
              permissionIdentification: "mobile-menu-myMaintenance",
            },
          ];
        } else {
          this.tabList = [
            {
              name: "全部",
              num: 0,
              status: "",
              permissionIdentification: "mobile-menu-maintenanceManage",
            },
            {
              name: "待派单",
              num: 0,
              status: 6,
              permissionIdentification: "mobile-menu-maintenanceManage",
            },
            {
              name: "已退回",
              num: 0,
              status: 14,
              permissionIdentification: "mobile-menu-maintenanceManage",
            },
          ];
        }
        let response = await getStatisticList();
        let workOrderStatisticsListData;
        if (response.code == 200 || response.code == 0) {
          if (workType == 1) {
            workOrderStatisticsListData = response.data.find((item) => {
              return (
                item.permissionIdentification == "mobile-menu-myReportRepair"
              );
            })?.workOrderStatisticsList;
          }
          if (workType == 2) {
            workOrderStatisticsListData = response.data.find((item) => {
              return (
                item.permissionIdentification == "mobile-menu-myMaintenance"
              );
            })?.workOrderStatisticsList;
          }
          if (workType == 3) {
            workOrderStatisticsListData = response.data.find((item) => {
              return (
                item.permissionIdentification == "mobile-menu-maintenanceManage"
              );
            })?.workOrderStatisticsList;
          }
        }
        // const workOrderStatisticsListData = uni.getStorageSync(
        //   "workOrderStatisticsListData"
        // );
        console.log(workOrderStatisticsListData);

        // 设备tab页上面的数量
        this.tabList.map((item) => {
          workOrderStatisticsListData.map((item1) => {
            if (item.status == item1.status) {
              item.num = item1.countTotal;
            }
          });
        });
        if (workOrderStatisticsListData[0]?.status == "88") {
          const {
            data: { workOrderStatisticsList },
          } = await getMyReportRepairStatistics();
          this.tabList.map((item) => {
            workOrderStatisticsList.map((item1) => {
              if (item.status == item1.status) {
                item.num = item1.countTotal;
              }
            });
          });
        }

        // 汇总全部
        this.tabList[0].num = this.tabList.reduce((pre, cur) => {
          if (cur.num) pre += cur.num;
          return pre;
        }, 0);
        this.currentData = this.tabList[this.currentTab];
      },
      // 获取报修状态字典
      async getMyReportDict() {
        const {
          data: { list },
        } = await getMyReportRepairDict({
          pageSize: 100,
          dictType: "repair_process_status",
        });
        this.dictList = list;
      },
      searchFn() {
        this.selectValue = this.searchValue.trim();
      },
      clearFn() {
        this.selectValue = "";
      },
      initialize(option) {
        this.setTabList(option.workType);
        if (option.workType == "1") {
          this.pageTitle = "报修记录";
        } else if (option.workType == "2") {
          this.pageTitle = "我的维修";
        } else {
          this.pageTitle = "维修管理";
        }
        // 未处理 = 待派单、待接单、待到场
        // 处理中 = 处理中
        // 待验收 = 待验收
        // 已完成 = 已完成、已驳回、已撤回
        // 报修记录 = 全部
        if (option.type) {
          switch (option.type) {
            case "待派单":
              this.currentTab = 1;
              break;
            case "待接单":
              this.currentTab = 1;
              break;
            case "待到场":
              this.currentTab = 2;
              break;
            case "处理中":
              this.currentTab = 3;
              break;
            case "已退回":
              this.currentTab = 2;
              break;
            case "报修记录":
              this.currentTab = 0;
              break;
            default:
              this.currentTab = 0;
          }
        }

        // 默认调用一次
        if (option.workType == "1") this.change(this.tabList[0], 0);
        else {
          const currentIndex =
            this.tabList.findIndex((item) => item.status == option.status) != -1
              ? this.tabList.findIndex((item) => item.status == option.status)
              : 0;
          const currentItem = this.tabList[currentIndex];
          this.change(currentItem, currentIndex);
        }
      },
    },
    mounted() {
      this.getMyReportDict();
    },
    onReachBottom() {
      this.pageSize += 10;
    },
    onLoad(option) {},
    onShow() {
      const option = {
        type: this.$route.query.type || null,
        status: this.$route.query.status || null,
        workType: this.$route.query.workType || null,
      };
      this.initialize(option);
    },
  };
</script>

<style>
  .page {
    background: rgba(0, 0, 0, 0.02);
    min-height: 100vh;
  }

  .searchInput {
    padding: 25rpx 40rpx;
    background: #fff;
  }

  .grid-item-box {
    text-align: center;
  }

  .gridClass {
    width: 90%;
    margin: 0 auto;

    .gridItemClass {
      font-family: MiSans, MiSans;
      margin: 10rpx 0;
      font-size: 28rpx;
      line-height: 44rpx;
      font-weight: 500;
      /* color: rgba(0,0,0,0.6) !important; */
    }
  }
</style>
