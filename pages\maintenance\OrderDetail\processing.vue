<template>
	<view class="page">
		<uni-card class="card">
			<view class="content">
				<!-- <view class="content-item">
					<view class="item-left">总时长</view>
					<view class="item-right">6小时44分</view>
				</view>
				<view class="content-item">
					<view class="item-left">维修耗时</view>
					<view class="item-right">46分钟</view>
				</view> -->
				<!-- <u-divider text="进度" style="margin: 10rpx;"></u-divider> -->
				<u-steps class="steps" current="1" direction="column"  dot activeColor='#0E4AE0'>
					<u-steps-item :title="getDictName(item.title)"  v-for="(item,index) in processingList">
						<text :class="index===processingList.length-1 ? 'slot-icon-processing' : 'slot-icon-done'" slot="icon"></text>
						<view class="desc" slot="desc">
							<view class="left">
								{{item.name}}
							</view>
							<view class="right">
								{{item.time}}
							</view>
						</view>
					</u-steps-item>
				</u-steps>
			</view>
		</uni-card>
	</view>
</template>

<script>
	import {getWorkOrderFlow,getMyReportRepairDict} from '@/api/reportRepair/index.js'
	export default {
		data() {
			return {
				processingList:[],
				dictList:[{
					
				}]
			}
		},
		methods: {
			async getWorkOrderFlow(){
				const pages = getCurrentPages(); // 获取页面栈
				const currentPage = pages[pages.length - 1]; // 获取当前页面实例
				const options = currentPage.options; // options 就是路由参数对象
				const {data} = await getWorkOrderFlow({
					id:options.workOrderId
				})
				this.processingList = data.map(item=>{
					return {
						title:item.status,
						name:'用户：'+item.handlePerson,
						time:item.createTime,
					}
				})
			},
			// 获取字典值
			getDictName(status){
				let result = ''
				uni.getStorage({
				  key: 'processDict',
				  success: (res) => {
					result = res.data.find(item=>item.value==status)?.label
				  },
				  fail: () => {
				  }
				});
				return result
			},
		},
		mounted() {
			this.getWorkOrderFlow()
		}
	}
</script>

<style lang="scss">
	.content {
		width: 100%;
		height: 100%;

		.content-item {
			margin-top: 10rpx;
			display: flex;
			justify-content: space-between;
			width: 100%;
			font-family: MiSans, MiSans;
			font-size: 28rpx;
			color: rgba(0, 0, 0, 0.9);
			line-height: 20rpx;
			text-align: right;
			font-style: normal;
			text-transform: none;
			padding-bottom: 20rpx;

			.item-right {
				width: 75%;
				height: 100%;

				img {
					vertical-align: bottom;
					margin-right: 5rpx
				}
			}

			:nth-last-child(1) {
				padding-bottom: none;
			}
		}
	}

	.slot-icon-done {
		width: 8px;
		height: 8px;
		background: #0E4AE0;
		border-radius: 999px;
	}
	.slot-icon-processing {
		box-sizing: border-box;
		width: 8px;
		height: 8px;
		border-radius: 999px;
		border: 2px solid #0E4AE0;
	}
	.steps{
		.desc{
			position: relative;
			display: flex;
			justify-content: space-between;
			height: 60rpx;
			.left{
				margin-top: 10rpx;
				font-family: MiSans, MiSans;
				font-weight: 400;
				font-size: 14px;
				color: rgba(0,0,0,0.9);
				line-height: 22px;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
			.right{
				position: absolute;
				right: 0;
				top:-40rpx;
			}
		}
	}
</style>