<template>
  <view class="page">
    <topInfo
      title="维修上报"
      :goback="true"
      :isShowBack="false"
      backtype="navigate"
      :isDark="true"
      backgroundColor="rgba(1.0,1.0,1.0,0.0)"
      :isShadow="false"
    ></topInfo>
    <uni-card class="card">
      <u--form
        class="formStyle"
        label-width="200rpx"
        labelPosition="left"
        :model="repairInfoData"
        :rules="rules"
        ref="uForm"
      >
        <u-form-item
          label="维修日期"
          prop="repairDate"
          @click="repairDateClick"
          borderBottom
          ref="item1"
        >
          <u--input
            v-model="repairInfoData.repairDate"
            placeholder="请选择维修日期"
            border="none"
            clearable
            inputAlign="right"
            disabled
            disabledColor="#ffffff"
            suffixIcon="arrow-right"
          ></u--input>
        </u-form-item>
        <u-form-item
          label="故障类型"
          prop="faultTypeLabel"
          borderBottom
          ref="item2"
          @click="faultTypeClick"
        >
          <u--input
            v-model="repairInfoData.faultTypeLabel"
            placeholder="请选择故障类型"
            border="none"
            clearable
            inputAlign="right"
            disabled
            disabledColor="#ffffff"
            suffixIcon="arrow-right"
          ></u--input>
        </u-form-item>
        <u-form-item
          label="故障原因"
          labelPosition="top"
          prop="faultReason"
          borderBottom
          ref="item3"
          :required="true"
        >
          <u--textarea
            style="margin-top: 5px"
            v-model="repairInfoData.faultReason"
            placeholder="请输入故障原因"
            inputAlign="right"
            count
            maxlength="100"
          ></u--textarea>
        </u-form-item>
        <u-form-item
          label="维修内容"
          labelPosition="top"
          prop="repairContent"
          borderBottom
          ref="item4"
          :required="true"
        >
          <u--textarea
            style="margin-top: 5px"
            v-model="repairInfoData.repairContent"
            placeholder="请输入维修内容"
            inputAlign="right"
            count
            maxlength="120"
          ></u--textarea>
        </u-form-item>
        <u-form-item
          label="维修程度"
          labelPosition="top"
          prop="repairGrade"
          borderBottom
          ref="item4"
        >
          <u-radio-group v-model="repairInfoData.repairGrade" placement="row">
            <u-radio
              v-for="(item, index) in repairGradeList"
              :key="index"
              :label="item.label"
              :name="item.value"
              style="margin-right: 10rpx"
            >
            </u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item
          label="维修结果"
          labelPosition="top"
          prop="repairResult"
          borderBottom
          ref="item4"
        >
          <u-radio-group v-model="repairInfoData.repairResult" placement="row">
            <u-radio
              v-for="(item, index) in repairResultList"
              :key="index"
              :label="item.label"
              :name="item.value"
              style="margin-right: 10rpx"
            >
            </u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item
          label="现场照片"
          labelPosition="top"
          prop="photoUrlList"
          borderBottom
          ref="item5"
        >
          <u-upload
            :fileList="fileList1"
            uploadIcon="plus"
            @afterRead="afterRead"
            @delete="deletePic"
            name="1"
            multiple
            :maxCount="10"
            style="margin-top: 10rpx"
          ></u-upload>
        </u-form-item>
      </u--form>
      <u-datetime-picker
        :show="isShowDate"
        v-model="repairInfoData.repairDate"
        mode="date"
        :closeOnClickOverlay="true"
        @close="isShowDate = false"
        @cancel="isShowDate = false"
      >
      </u-datetime-picker>

      <u-action-sheet
        :show="isShowfaultType"
        title="请选择故障类型"
        @close="isShowfaultType = false"
        @select="faultTypeSelect"
      >
        <faultTypeSelectCom @selectData="selectData" />
      </u-action-sheet>
    </uni-card>
    <u-tabbar :fixed="true" :placeholder="true" :safeAreaInsetBottom="true">
      <u-button
        color="#0E4AE0"
        type="primary"
        text="提交"
        style="height: 70rpx; margin: 6px 10px"
      ></u-button>
    </u-tabbar>
  </view>
</template>

<script>
  import { getMyReportRepairDetail } from "@/api/reportRepair/index.js";
  import faultTypeSelectCom from "./tuiCascadeSelection/index.vue";
  export default {
    components: { faultTypeSelectCom },
    data() {
      return {
        isShowDate: false,
        isShowfaultType: false,
        repairInfoData: {
          repairDate: "",
          faultType: "",
          faultTypeLabel: "",
          faultReason: "",
          repairContent: "",
          repairGrade: "0",
          repairResult: "0",
          photoUrlList: [],
        },
        fileList1: [],
        savedData: null,
        repairGradeList: [
          {
            label: "无修",
            name: "0",
            value: "0",
            disabled: false,
          },
          {
            label: "小修",
            value: "1",
            disabled: false,
          },
          {
            label: "一般",
            value: "2",
            disabled: false,
          },
          {
            label: "大修",
            value: "3",
            disabled: false,
          },
        ],
        repairResultList: [
          {
            label: "已解决",
            value: "0",
            disabled: false,
          },
          {
            label: "部分解决",
            value: "1",
            disabled: false,
          },
          {
            label: "未解决",
            value: "2",
            disabled: false,
          },
        ],
        rules: {
          faultReason: {
            type: "string",
            required: true,
            message: "请输入故障原因",
            trigger: ["blur", "change"],
          },
          repairContent: {
            type: "string",
            required: true,
            message: "请输入故障内容",
            trigger: ["blur", "change"],
          },
        },
      };
    },
    methods: {
      repairDateClick() {
        this.isShowDate = true;
      },
      faultTypeClick() {
        this.isShowfaultType = true;
      },
      selectData(value, label) {
        this.repairInfoData.faultType = value;
        this.repairInfoData.faultTypeLabel = label;
      },
    },
    mounted() {},
    onLoad(option) {},
  };
</script>

<style lang="scss" scoped>
  .page {
    height: 1200rpx;
    background-image: url(@/static/images/home/<USER>
    background-size: contain;
    /* 背景图等比缩放充整个容器 */
    background-position: center;
    /* 背景图居中对齐 */
  }

  .content {
    width: 100%;
    height: 100%;

    .content-item {
      margin-top: 20rpx;
      display: flex;
      justify-content: space-between;
      width: 100%;
      font-family: MiSans, MiSans;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.9);
      line-height: 30rpx;
      text-align: right;
      font-style: normal;
      text-transform: none;
      border-bottom: 1rpx solid #e7e7e7;
      padding-bottom: 20rpx;

      .item-left {
        // width: 40%;
      }

      .item-right {
        width: 75%;
        // padding-left: 30rpx;
        height: 100%;

        img {
          vertical-align: bottom;
          margin-right: 5rpx;
        }
      }
    }

    :nth-last-child(1) {
      border: 0;
    }
  }
</style>
