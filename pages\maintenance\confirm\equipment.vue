<template>
  <view class="page">
    <topInfo
      title="选择设备"
      backtype="navigateBack"
      :isShowBack="true"
      :isDark="false"
      backgroundColor="#fff"
    ></topInfo>
    <u--input
      placeholder="请输入设备名称"
      v-model="params.equipmentName"
      prefixIcon="search"
      shape="circle"
      style="margin: 20rpx 40rpx 10rpx; height: 50rpx"
      prefixIconStyle="font-size: 44rpx;color: #909399"
      @blur="getEquipmentList"
    ></u--input>
    <u-list
      :loading="loading"
      :isLoadMore="isLoadMore"
      @scrolltolower="scrolltolower"
      style="height: 74vh; overflow: scroll"
      :pagingEnabled="true"
    >
      <u-list-item v-for="(item, index) in indexList" :key="index">
        <view
          class="item-content"
          :class="{
            active: activeIndex === index,
          }"
          @click="itemClick(item, index)"
        >
          <!-- <equipmentItem :itemData="item" @itemClick="itemClick" /> -->
          <view class="itemClass">
            <view class="left">
              <u--image
                :showLoading="true"
                :src="item.imageUrl"
                width="160rpx"
                height="160rpx"
              ></u--image>
            </view>
            <view class="right">
              <view class="right-top">
                <view>
                  {{ item.equipmentName }}
                </view>
              </view>
              <view class="content">
                <view class="content-item">
                  <view class="item-left">设备编号：</view>
                  <view class="item-right">{{ item.equipmentCode }}</view>
                </view>
                <view class="content-item">
                  <view class="item-left">使用部门：</view>
                  <view class="item-right">{{
                    getDept(item.equipmentDepartment)
                  }}</view>
                </view>
                <view class="content-item">
                  <view class="item-left">存放位置：</view>
                  <view class="item-right">{{
                    getLocationName(item.equipmentLocation)
                  }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </u-list-item>
    </u-list>
    <u-button
      color="#0E4AE0"
      type="primary"
      :disabled="!currentItem"
      text="提交"
      style="width: 700rpx; height: 70rpx; margin: 6rpx auto"
      @click="submit"
    ></u-button>
  </view>
</template>

<script>
  import {
    getEquipmentPage,
    getEquipmentConfigPage,
    getSimpleDeptList,
  } from "@/api/reportRepair/index.js";
  import equipmentItem from "./equipmentItem.vue";
  export default {
    components: {
      equipmentItem,
    },
    data() {
      return {
        indexList: [],
        activeIndex: -1,
        params: {
          pageNo: 1,
          pageSize: 15,
          equipmentName: "",
        },
        isLoadMore: true, // 是否可以加载更多数据
        loading: false,
        currentItem: null,
        deptList: [],
        locationList: [],
      };
    },
    methods: {
      itemClick(item, index) {
        this.currentItem = item;
        this.activeIndex = index;
      },
      scrolltolower(scrollTop) {
        this.loadmore();
      },
      async getEquipmentList() {
        const {
          data: { list },
        } = await getEquipmentPage(this.params);
        this.indexList = list;
      },
      async loadmore() {
        if (this.loading || !this.isLoadMore) return;
        this.loading = true;
        try {
          // 将新数据追加到列表中
          const {
            data: { list },
          } = await getEquipmentPage(this.params);
          this.indexList = [...this.indexList, ...list];
          // 判断是否还有更多数据
          if (list.length < this.params.pageSize) {
            // 假设接口每页返回10条数据
            this.isLoadMore = false;
          } else {
            this.params.pageNo += 1;
          }
        } catch (error) {
          console.error("加载数据出错：", error);
        } finally {
          this.loading = false;
        }
      },
      submit() {
        if (this.currentItem) {
          //   uni.navigateTo({
          //     url:
          //       "/pages/maintenance/confirm/index" +
          //       "?equipmentId=" +
          //       this.currentItem.id +
          //       "&" +
          //       "equipmentName=" +
          //       this.currentItem.equipmentName +
          //       "&workOrderId=" +
          //       this.workOrderId,
          //   });
          this.$tab.navigateBackWithParams({
            equipmentId: this.currentItem.id,
            equipmentName: this.currentItem.equipmentName,
          });
        }
      },
      async getLocation() {
        const res = await getEquipmentConfigPage({
          type: "location",
          pageNo: 1,
          pageSize: 100,
        });
        this.locationList = res.data.list;
      },
      async getDeptList() {
        const res = await getSimpleDeptList();
        this.deptList = res.data;
      },
      getDept(id) {
        return this.deptList.find((item) => item.id == id)?.name;
      },
      getLocationName(id) {
        return this.locationList.find((item) => item.id == id)?.name;
      },
    },
    mounted() {
      this.loadmore();
      this.getLocation();
      this.getDeptList();
    },
    onLoad(option) {
      console.log(option);
      this.workOrderId = option.workOrderId;
    },
  };
</script>

<style lang="scss" scoped>
  .page {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }
  .u-input {
    flex: unset;
  }
  .u-list {
    flex: 1;
  }
  .itemClass {
    height: 200rpx;
    margin: 0 40rpx;
    padding: 20rpx;
    border-bottom: 1rpx solid #e7e7e7;
    display: flex;
    justify-content: flex-start;

    .left {
      width: 200rpx;
    }

    .right {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-left: 30rpx;
      height: 160rpx;

      .right-top {
        font-weight: 600;
      }

      .content {
        width: 500rpx;
        border-radius: 6px 6px 6px 6px;
        margin-top: 10rpx;
        color: rgba(0, 0, 0, 0.6);

        .content-item {
          display: flex;
          justify-content: space-between;
          width: 100%;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 28rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;

          .item-left {
            width: 30%;
          }

          .item-right {
            width: 70%;
          }
        }

        :nth-child(1) {
          margin-top: 0rpx;
        }
      }
    }
  }
  .active {
    background-color: #e0f7fa; /* 点击后的高亮颜色 */
  }
</style>
