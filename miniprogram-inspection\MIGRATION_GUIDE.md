# 巡检管理模块小程序迁移指南

## 1. 页面结构迁移

### 1.1 页面文件结构调整
uni-app的页面结构需要调整为小程序的页面结构：

**uni-app结构：**
```
pages/inspection/index.vue
```

**小程序结构：**
```
pages/inspection/index.js
pages/inspection/index.wxml
pages/inspection/index.wxss
pages/inspection/index.json
```

### 1.2 页面配置迁移
将uni-app的页面配置迁移到小程序的页面配置文件中：

**uni-app (pages.json):**
```json
{
  "path": "pages/inspection/index",
  "style": {
    "navigationBarTitleText": "巡检列表"
  }
}
```

**小程序 (pages/inspection/index.json):**
```json
{
  "navigationBarTitleText": "巡检列表",
  "usingComponents": {}
}
```

## 2. 模板语法迁移

### 2.1 基本语法对比
| uni-app | 小程序 | 说明 |
|---------|--------|------|
| `<template>` | `<view>` | 根节点 |
| `v-if` | `wx:if` | 条件渲染 |
| `v-for` | `wx:for` | 列表渲染 |
| `@click` | `bindtap` | 事件绑定 |
| `{{}}` | `{{}}` | 数据绑定（相同） |

### 2.2 示例迁移
**uni-app模板：**
```vue
<template>
  <view class="container">
    <view v-for="item in list" :key="item.id" @click="handleClick(item)">
      {{item.name}}
    </view>
  </view>
</template>
```

**小程序模板：**
```xml
<view class="container">
  <view wx:for="{{list}}" wx:key="id" bindtap="handleClick" data-item="{{item}}">
    {{item.name}}
  </view>
</view>
```

## 3. 脚本逻辑迁移

### 3.1 页面生命周期对比
| uni-app | 小程序 | 说明 |
|---------|--------|------|
| `onLoad` | `onLoad` | 页面加载 |
| `onShow` | `onShow` | 页面显示 |
| `onReady` | `onReady` | 页面初次渲染完成 |
| `onHide` | `onHide` | 页面隐藏 |
| `onUnload` | `onUnload` | 页面卸载 |

### 3.2 数据和方法迁移
**uni-app：**
```javascript
export default {
  data() {
    return {
      list: []
    }
  },
  methods: {
    handleClick(item) {
      // 处理点击
    }
  },
  onLoad() {
    // 页面加载
  }
}
```

**小程序：**
```javascript
Page({
  data: {
    list: []
  },
  handleClick(e) {
    const item = e.currentTarget.dataset.item;
    // 处理点击
  },
  onLoad() {
    // 页面加载
  }
})
```

## 4. API请求迁移

### 4.1 请求方法迁移
**uni-app (utils/request.js)：**
```javascript
import request from '@/utils/request'

export function getInspectionInfo(data) {
  return request({
    url: '/devops/app/work-order/getByStatus',
    method: 'GET',
    data: data
  })
}
```

**小程序：**
```javascript
// utils/request.js
function request(options) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: options.url,
      method: options.method || 'GET',
      data: options.data,
      success: resolve,
      fail: reject
    })
  })
}

// api/inspection.js
export function getInspectionInfo(data) {
  return request({
    url: '/devops/app/work-order/getByStatus',
    method: 'GET',
    data: data
  })
}
```

## 5. 样式迁移

### 5.1 样式文件迁移
**uni-app (.vue文件中的style)：**
```vue
<style lang="scss" scoped>
.container {
  padding: 20rpx;
  .item {
    margin-bottom: 10rpx;
  }
}
</style>
```

**小程序 (.wxss文件)：**
```css
.container {
  padding: 20rpx;
}
.container .item {
  margin-bottom: 10rpx;
}
```

### 5.2 注意事项
- 小程序不支持scss，需要转换为普通CSS
- 小程序不支持scoped，需要注意样式命名冲突
- 部分CSS属性可能不支持，需要测试调整

## 6. 组件迁移

### 6.1 自定义组件迁移
**uni-app组件：**
```vue
<template>
  <view class="top-info">
    <text>{{title}}</text>
  </view>
</template>

<script>
export default {
  props: ['title']
}
</script>
```

**小程序组件：**
```javascript
// components/topInfo/index.js
Component({
  properties: {
    title: String
  }
})
```

```xml
<!-- components/topInfo/index.wxml -->
<view class="top-info">
  <text>{{title}}</text>
</view>
```

### 6.2 第三方组件替换
- uview-ui → WeUI或其他小程序UI库
- uni-card → 自定义card组件
- uni-icons → 小程序原生icon或自定义图标

## 7. 路由导航迁移

### 7.1 页面跳转迁移
**uni-app：**
```javascript
uni.navigateTo({
  url: '/pages/inspectionDetail/index?id=123'
})
```

**小程序：**
```javascript
wx.navigateTo({
  url: '/pages/inspectionDetail/index?id=123'
})
```

## 8. 存储和状态管理

### 8.1 本地存储迁移
**uni-app：**
```javascript
uni.setStorageSync('key', value)
const value = uni.getStorageSync('key')
```

**小程序：**
```javascript
wx.setStorageSync('key', value)
const value = wx.getStorageSync('key')
```

## 9. 迁移步骤建议

1. **创建小程序项目结构**
2. **迁移简单页面**（如inspectionSuccess）
3. **迁移API接口和工具函数**
4. **迁移复杂页面**（如inspectionExecute）
5. **迁移组件**
6. **样式调整和测试**
7. **功能测试和优化**

## 10. 常见问题和解决方案

### 10.1 样式问题
- 使用rpx单位保持响应式
- 注意小程序的样式限制
- 测试不同设备的兼容性

### 10.2 API问题
- 配置请求域名白名单
- 处理跨域问题
- 实现请求拦截器

### 10.3 组件问题
- 替换不兼容的组件
- 实现自定义组件
- 处理组件通信

这个迁移指南提供了详细的步骤和示例，帮助您将uni-app的巡检管理模块成功迁移到小程序平台。
