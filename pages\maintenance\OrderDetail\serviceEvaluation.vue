<template>
	<view class="page">
		<view v-if="isShowInfo">
			<uni-card class="card" padding="10rpx" >
				<u-list class="rate">
					<u-list-item v-for="(item, index) in rateList" :key="index">
						<view class="rateItem">
							<view class="rateLeft">{{item.name}}</view>
							<u-rate :count="item.rateCount" :value="item.rateValue" readonly active-color="#FF9C00"
								inactive-color="#DCDCDC" class="rateContent"></u-rate>
							<view class="rateRight">{{item.rate}}</view>
						</view>
					</u-list-item>
				</u-list>
			</uni-card>
			<uni-card class="card" padding="10rpx 0 40rpx" title="服务评价">
				<template v-slot:title>
					<view class="title">
						服务评价
					</view>
				</template>
				<view class="content">
					<view class="content-title">
						{{supplementary}}
					</view>
				</view>
			</uni-card>
		</view>
		<u-empty
		        mode="data"
				icon='https://cdn.uviewui.com/uview/empty/list.png'
				v-else
		>
		</u-empty>
	</view>
</template>

<script>
	import {getEvaluate} from '@/api/reportRepair/index.js'
	export default {
		components: {},
		data() {
			return {
				isShowInfo:false,
				rateList: [{
						name: '响应速度',
						value:'responseSpeed',
						rateCount: 5,
						rateValue: 0,
						rate: '一般'
					},
					{
						name: '服务态度',
						value:'serviceEvaluation',
						rateCount: 5,
						rateValue: 0,
						rate: '比较好'
					},
					{
						name: '专业水平',
						value:'professionalLevel',
						rateCount: 5,
						rateValue: 0,
						rate: '比较好'
					},
					{
						name: '问题解决',
						value:'problemSolving',
						rateCount: 5,
						rateValue: 0,
						rate: '比较好'
					}
				],
				supplementary:''
			}
		},
		methods: {
			async getEvaluate(){
				const pages = getCurrentPages(); // 获取页面栈
				const currentPage = pages[pages.length - 1]; // 获取当前页面实例
				const options = currentPage.options; // options 就是路由参数对象
				const {data} = await getEvaluate({
					workOrderId:options.workOrderId
				})
				if(data){
					this.isShowInfo = true
					this.rateList.map(item=>{
						item.rateValue =data[item.value]
					})
					this.supplementary = data.supplementary
				}else{
					this.isShowInfo = false
				}
				
			}
		},
		mounted() {
			this.getEvaluate()
		}
	}
</script>

<style scoped lang="scss">
	.rate {
		height: 320rpx !important;

		.rateItem {
			display: flex;
			justify-content: space-between;
			height: 80rpx;
			line-height: 80rpx;
			border-bottom: 1px solid #E7E7E7;

			.rateLeft {
				width: 25%;
				font-family: MiSans, MiSans;
				font-weight: 400;
				font-size: 28rpx;
				color: rgba(0, 0, 0, 0.6);
				line-height: 80rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.rateContent {
				width: 55%;
			}

			.rateRight {
				width: 20%;
				font-family: MiSans, MiSans;
				font-weight: 400;
				font-size: 28rpx;
				color: rgba(0, 0, 0, 0.9);
				line-height: 80rpx;
				text-align: right;
				font-style: normal;
				text-transform: none;
			}
		}

	}


	.content {
		width: 100%;
		height: 100%;
		background: rgba(14, 74, 224, 0.04);
		border-radius: 4px;
		padding: 20rpx;
		.content-item {
			margin-top: 10rpx;
			display: flex;
			justify-content: space-between;
			font-family: MiSans, MiSans;
			font-weight: 400;
			font-size: 28rpx;
			color: rgba(0, 0, 0, 0.4);
			line-height: 40rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;

			.item-left {
				width: 30%;
			}

			.item-right {
				width: 88%;
				color: rgba(0, 0, 0, 0.9)
			}
		}

		:nth-child(1) {
			margin-top: 0rpx;
		}
		
		.content-title{
			font-family: MiSans, MiSans;
			font-weight: 400;
			font-size: 28rpx;
			color: rgba(0,0,0,0.9);
			line-height: 44rpx;
			text-align: justified;
			font-style: normal;
			text-transform: none;
		}
	}

	.title {
		margin: 20rpx 0;
		font-weight: 400;
	}
</style>