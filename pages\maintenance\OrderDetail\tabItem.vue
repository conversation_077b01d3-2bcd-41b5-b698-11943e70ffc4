<template>
	<view class="tablItem">
		<repairInformation v-if="currentTab==0"></repairInformation>
		<processing v-if="currentTab==1"></processing>
		<maintenanceInformation v-if="currentTab==2"></maintenanceInformation>
		<serviceEvaluation v-if="currentTab==3"></serviceEvaluation>
	</view>
</template>

<script>
	import repairInformation from './repairInformation.vue'
	import processing from './processing.vue'
	import maintenanceInformation from './maintenanceInformation.vue'
	import serviceEvaluation from './serviceEvaluation.vue'
	export default {
		components: {
			repairInformation,
			processing,
			maintenanceInformation,
			serviceEvaluation
		},
		props: ['currentTab'],
		data() {
			return {
				
			}
		},
		methods: {
			
		},
		mounted() {
		
		}
	}
</script>

<style lang="scss">
	.tablItem {
		min-height: 100vh;
		height: 100%;
		padding-bottom: 20rpx;
	}
</style>