<template>
	<view class="">
		<topInfo title="巡检人任务详情" url="/pages/inspection/index" :isShowBack="true" backType="nagivate"></topInfo>
		<view class="totalInfo">
			<uni-card title="主变压器设备巡检任务" sub-title="XJDHUYB127565" type="line" is-full extra="待派单">
				<template v-slot:title>
					<view class=""
						style="display: flex;flex-direction: row;justify-content: space-between;border-bottom: 1px solid #f2f2f2;">
						<view class="">
							<view class="" style="font-size: 16px;font-weight: 550;">主变压器设备巡检任务</view>
							<view class="" style="font-size: 13px;color: rgba(0, 0, 0, 0.4);margin-top: 2px;">
								XJDHUYB127565</view>
						</view>
						<view class="">
							<view style="display: flex;align-items: center;justify-content: center;height: 100%;">
								<uni-tag style="margin-left: 10rpx;" text="待派单" type="warning" :circle="true"></uni-tag>
							</view>
						</view>
					</view>
				</template>
				<view class="basicInfo">
					<view class="basicInfoDetail">
						<view class="basicInfoTitle">巡检时间:</view>
						<view class="basicInfoContent">08:00 至 12:00</view>
					</view>
					<view class="basicInfoDetail">
						<view class="basicInfoTitle">截止日期:</view>
						<view class="basicInfoContent">2021-01-01</view>
					</view>
					<view class="basicInfoDetail">
						<view class="basicInfoTitle">巡检人:</view>
						<view class="basicInfoContent">李某</view>
					</view>
					<view class="basicInfoDetail">
						<view class="basicInfoTitle">负责人:</view>
						<view class="basicInfoContent">王某</view>
					</view>
					<view class="basicInfoDetail">
						<view class="basicInfoTitle">巡检要求:</view>
						<view class="basicInfoContent">1、检查设备表面是否有损坏<br>
							2、观察设备的指示灯、仪表读数等是否显示正常。</view>
					</view>
					<view class="basicInfoDetail">
						<view class="basicInfoTitle">备注:</view>
						<view class="basicInfoContent">无</view>
					</view>
					<view class="basicInfoBottom">
						<!-- <uni-icons custom-prefix="iconfont" type="icon-jilu" size="18" color="#f3a73f"></uni-icons> -->
						<uni-icons custom-prefix="iconfont" type="icon-tixing" size="18" color="#f3a73f"
							style="border:1px dashed #f3a73f"></uni-icons>
						<text style="margin-left: 16rpx;">请于2024-09-30前完成巡检</text>
						<!-- 请于2024-09-30前完成巡检 -->
					</view>
				</view>
			</uni-card>
		</view>
		<view class="detailInfo">
			<uni-segmented-control :current="current" :values="items" @clickItem="onClickItem" styleType="text"
				activeColor="#0e4ae0"></uni-segmented-control>
			<view class="content">
				<view v-show="current === 0" style="padding: 10px;">
					<uni-collapse v-for="(item,index) in inspectionInfo" style="margin-top: 10rpx;">
						<uni-collapse-item ref="collapse" titleBorder="none" :open="true">
							<template v-slot:title>
								<view class="slot-content">
									<view class="" style="border-left:4px #f3a73f solid;">
										<text style="margin-left: 10px;">{{item.floor}}</text>
									</view>
									<view class="" style="color: #a0a1a4;">{{item.completePercent}}</view>
								</view>
							</template>
							<view class="slot-content-content" style="">
								<uni-collapse v-for="(floorItem,floorIndex) in item.floorInfo "
									style="margin-top: 10rpx;" @change="handleHeight">
									<uni-collapse-item titleBorder="none">
										<template v-slot:title>
											<view class="slot-content">
												<view class="">
													<text style="margin-left: 10px;">{{floorItem.deviceName}}</text>
												</view>
												<view class="" style="color: #a0a1a4;">{{floorItem.totalCheckNum}}
												</view>
												<view class=""
													:style="{color:floorItem.isComplete? '#40c28c':'#f75965'}">
													{{floorItem.isComplete == true?'已完成':'未完成'}}
												</view>
											</view>
										</template>
										<view class="slot-content-content">
											<text class="text">折叠内容主体，可自定义内容及样式</text>
										</view>
									</uni-collapse-item>
								</uni-collapse>
							</view>
						</uni-collapse-item>
					</uni-collapse>
				</view>
				<view v-show="current === 1">
					<uni-card>
						<view class="processInfo">
							<view class="bg">
								<view class="steps">
									<view class="steps_item" v-for="(i, processIndex) in process" :key="processIndex">
										<view class="s_r">
											<view class="s_r_content" style="margin-top: 5px;">
												<view class="index"
													:style="{ backgroundColor: !i.isAudit ? '#EAEAEA' : '#0e4ae0' }"></view>
												<view class="line"
													:style="{ backgroundColor: !i.isAudit ? '#EAEAEA' : '#0e4ae0' }">
												</view>
											</view>
											<!-- <view class="line"
												:style="{ backgroundColor: !i.isAudit ? '#EAEAEA' : '#0e4ae0' }">
											</view> -->
											<!-- <view class="s_r_blank">
												&nbsp
											</view> -->

										</view>
										<view class="s_l">
											<view class="info_item">
												<view class="top_info">
													<view class="title">{{ i.title }}</view>
													<view class="date">{{ i.time }}</view>
												</view>
												<view class="info">
													<template>
														<view class="text-grey">
															{{ i.name }} <text class="ml5"></text>
														</view>
													</template>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</uni-card>

				</view>
			</view>
			<view class="bottom-button">
				<button @click="toggle('bottom')" class="mini-btn" type="primary" size="mini" style="width: 160px;">分配巡检人员</button>
				<button @click="inspectionExecute" class="mini-btn" type="primary" size="mini"
					style="margin-left: 10px;width: 160px;">执行巡检</button>
			</view>
			
			
			<view class="popupInfo">
				<uni-popup ref="popup" type="bottom" border-radius="10px 10px 0 0" background-color="#fff">
					<view class="popup-title">
						<view class="popup-title-style">
							分配巡检人员
						</view>
						<view class="popup-title-icon">
							<uni-icons type="closeempty" size="30" @click="close"></uni-icons>
						</view>
					</view>
					<view class="" style="margin-top: 20px;width: 100%;height: 100%;">
						<view class="">
							添加巡检执行人员
						</view>
						<view class="search-title">
							<uni-search-bar class="searchBar" @confirm="search" :focus="true" v-model="workOrderName"
								clearButton="auto" cancelButton="none" @cancel="cancel" @clear="clear" placeholder="请输入姓名">
							</uni-search-bar>
							<button @click="searchData" size="default" type="default"
								style="display: flex;line-height: 34px;;font-size: 30rpx;width: 68px;height: 34px;text-align:center;background-color: #e7edfc;color: #0e4ae0;borderColor:#1AAD19">搜索</button>
						</view>
						<view class="popup-content">
							<checkbox-group @change="checkboxChange">
								<label class="uni-list-cell uni-list-cell-pd" v-for="(item,index) in humanInfo"
									:key="item.value"
									style="display: flex;flex-direction: row;justify-content: space-between;margin-top: 4px;text-align: left;">
									<view>
										<checkbox :value="item.value" :checked="item.checked" />
									</view>
									<view>{{item.name}}</view>
									<view>{{item.number}}</view>
								</label>
							</checkbox-group>
						</view>
						<view class="popup-bottom-button">
							<button @click="close" class="mini-btn" type="primary" size="mini"
								style="background-color: #e7edfc;color: #0e4ae0;width: 160px;">取消</button>
							<button class="mini-btn" type="primary" size="mini"
								style="margin-left: 10px;width: 160px;">确定</button>
						</view>
					</view>
				</uni-popup>
			</view>
			
			
		</view>
	</view>
</template>

<script>
	export default {
		onLoad: function() {},
		data() {
			return {
				items: ['巡检明细', '任务流程图'],
				current: 0,
				workOrderName:'',
				inspectionInfo: [{
					floor: "A栋12层101",
					completePercent: "1/4",
					floorInfo: [{
							deviceName: "设备1",
							totalCheckNum: "12项",
							isComplete: false,
							itemizeInfo: [{
								deviceName: "空调",
								isComplete: false,
							}, {
								deviceName: "吹风机",
								isComplete: false,
							}, {
								deviceName: "电视",
								isComplete: false,
							}, ]
						},
						{
							deviceName: "设备1",
							totalCheckNum: "12项",
							isComplete: true,
							itemizeInfo: [{
								deviceName: "空调",
								isComplete: false,
							}, {
								deviceName: "吹风机",
								isComplete: false,
							}, {
								deviceName: "电视",
								isComplete: false,
							}, ]
						},
						{
							deviceName: "设备1",
							totalCheckNum: "12项",
							isComplete: false,
							itemizeInfo: [{
								deviceName: "空调",
								isComplete: false,
							}, {
								deviceName: "吹风机",
								isComplete: false,
							}, {
								deviceName: "电视",
								isComplete: false,
							}, ]
						}
					]
				}, {
					floor: "B栋10层102",
					completePercent: "1/4",
					floorInfo: [{
						deviceName: "设备1",
						totalCheckNum: "12项",
						isComplete: false,
						itemizeInfo: [{
							deviceName: "空调",
							isComplete: false,
						}, {
							deviceName: "吹风机",
							isComplete: false,
						}, {
							deviceName: "电视",
							isComplete: false,
						}, ]
					}]
				}, ],
				process: [{
						title: '创建工单',
						name: '【系统】自动生成',
						isAudit: true,
						desc: '',
						time: '2024-01-02 12:00:00'
					},
					{
						title: '派单',
						name: '【负责人】李某',
						isAudit: true,
						desc: '',
						time: '2024-01-04 12:00:00'
					},
					{
						title: '已退回',
						name: '【巡检人员】记得',
						isAudit: false,
						desc: '退回原因退回原因退回原因退回原因退回原因退回原因退回12',
						time: '2024-01-06 12:00:00'
					}
				],humanInfo: [{
					value: "1",
					name: "安旭",
					number: '15566666666',
					checked: true
				}, {
					value: "2",
					name: "王二狗",
					number: '15566666666',
					checked: false
				}, {
					value: "3",
					name: "赵小三",
					number: '15566666666',
					checked: false
				}, {
					value: "4",
					name: "王二狗",
					number: '15566666666',
					checked: false
				}, {
					value: "5",
					name: "赵小三",
					number: '15566666666',
					checked: false
				}, {
					value: "6",
					name: "王二狗",
					number: '15566666666',
					checked: false
				}, {
					value: "7",
					name: "赵小三",
					number: '15566666666',
					checked: false
				}, {
					value: "8",
					name: "王二狗",
					number: '15566666666',
					checked: false
				}, {
					value: "9",
					name: "赵小三",
					number: '15566666666',
					checked: false
				}, {
					value: "10",
					name: "王二狗",
					number: '15566666666',
					checked: false
				}, {
					value: "11",
					name: "赵小三",
					number: '15566666666',
					checked: false
				}, {
					value: "12",
					name: "王二狗",
					number: '15566666666',
					checked: false
				}, {
					value: "13",
					name: "赵小三",
					number: '15566666666',
					checked: false
				}]
			}
		},
		methods: {
			onClickItem(e) {
				if (this.current != e.currentIndex) {
					this.current = e.currentIndex;
				}
			},
			// 修正后的代码
			handleHeight() {
				console.log('999')
				this.$nextTick(() => {
					this.$refs.collapse.resize()
				})
				this.$nextTick(() => {
					setTimeout(() => {
						console.log('33333')
						this.$refs.collapse.resize();
					}, 10)
				});
			},
			toggle() {
				// 通过组件定义的ref调用uni-popup方法 ,如果传入参数 ，type 属性将失效 ，仅支持 ['top','left','bottom','right','center']
				this.$refs.popup.open('bottom')
			},
			close() {
				this.$refs.popup.close('bottom')
			},
			checkboxChange(e) {
				var items = this.$data.humanInfo;
				var values = e.detail.value;
				for (var i = 0, lenI = items.length; i < lenI; ++i) {
					const item = items[i]
					// console.log(item)
					if (values.includes(item.value)) {
						this.$set(item, 'checked', true)
					} else {
						this.$set(item, 'checked', false)
					}
				}
			},
			inspectionExecute(){
				uni.navigateTo({
					url:"/pages/inspectionExecute/index"
				})
			}

		}
	}
</script>

<style lang="scss">
	page {
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		background-color: #fff;
		min-height: 100%;
		height: auto;
		background-color: #f2f2f2;
		// padding: 10px;
	}

	.totalInfo {
		margin-top: 10px;
		padding: 6px;
		width: 100%;
	}

	.basicInfo {
		width: 100%;
	}

	.basicInfoDetail {
		display: flex;
		flex-direction: row;
		justify-content: left;
		align-items: center;

		.basicInfoTitle {
			color: rgba(0, 0, 0, 0.4);
			min-width: 80px;
		}

		.basicInfoContent {
			text-align: left;
			height: 100%;
		}
	}

	.basicInfoBottom {
		background-color: #fff9f0;
		height: 30px;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #ff9c00;
	}

	.detailInfo {
		padding: 4px;
		height: 100%;
	}

	.slot-content {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		height: 30px;
		padding: 5px;
		background-color: #f6f8fe;
	}

	.slot-content-content {
		padding: 10px;
		overflow-y: auto;
	}

	/deep/ .uni-collapse-item__title {
		display: flex;
		width: 100%;
		box-sizing: border-box;
		flex-direction: row;
		align-items: center;
		transition: border-bottom-color .3s;
		background-color: #f6f8fe;
	}

	/deep/ .slot-content-content {
		padding: 10px;
		height: 100%;
		overflow-y: auto;
	}

	.bottom-button {
		display: flex;
		flex-direction: row;
		justify-content: center;
	}

	.processInfo {
		// background: rgba(14, 74, 224, 0.04);
		// padding: 10px;
	}
	.popup-bottom-button {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		height: 80rpx;
		margin-top: 40rpx;
	}
	
	.popupInfo {
		// height: 400px;
	}
	
	/deep/.uni-popup .uni-popup__wrapper {
		display: block;
		position: relative;
		height: 500px;
		padding: 4px 4px;
	}
	
	.popup-title {
		display: flex;
		flex-direction: row;
		height: 25px;
		justify-content: space-between;
	
		.popup-title-style {
			font-size: 20px;
			font-weight: 550;
			width: 100%;
			text-align: center;
		}
	
	}
	
	.popup-content {
		height: 300px;
		// background-color: #eee;
		padding: 20rpx;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
	}
	.search-title {
		margin-top: 30rpx;
		display: flex;
		height: 80rpx;
		width: 100%;
		align-items: center;
		justify-content: center;
		padding: 0 10px 0 0;
	
	}
	
	.searchBar {
		width: 100%;
	}
</style>

<style lang="scss">
	.bg {
		width: 100%;
	}

	.steps {
		display: flex;
		flex-direction: column;

		.steps_item {
			display: flex;
			flex-direction: row;
			margin-top: 10rpx;

			.s_r {
				padding: 0 8rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 30px;

				// margin-top: 15px;
				.s_r_content {
					height: 100%;
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 30px;

					.line {
						flex: 1;
						width: 5rpx;
						border-left: 4rpx dashed #fff;
					}

					.index {
						width: 24rpx;
						height: 24rpx;
						border-radius: 50rpx;
						border: 4rpx solid #e3eeff;
						box-sizing: border-box;
					}
				}
			}



			.s_l {
				display: flex;
				flex-direction: column;
				// padding: 10rpx 0;
				flex: 1;
				width: 100%;
				// width: 180rpx;

				.info_item {
					background-color: #ffffff;
					// margin-right: 10rpx;
					border-radius: 10rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					// padding: 10rpx 0;
					width: 100%;

					.top_info {
						display: flex;
						flex-direction: row;
						align-items: center;
						flex-wrap: nowrap;
						height: 40rpx;
						justify-content: space-between;
						width: 100%;
						// justify-content: space-between;

						.date {
							// width: 140px;
						}
					}

					text {
						font-size: 24rpx;
						font-weight: 500;
						color: rgba(51, 51, 51, 1);
					}

					.title {
						// width: calc(100vw - 330rpx);
						font-size: 28rpx;
						font-weight: 500;
						color: #5881e9;
						// color: rgba(102, 102, 102, 1);
						// overflow: hidden;
						// text-overflow: ellipsis;
						// display: -webkit-box;
						// flex-direction: column;
					}

					.info {
						font-size: 24rpx;
						color: #afb4be;
						margin-top: 10rpx;
					}

					.date {
						font-size: 23rpx;
						color: #afb4be;
					}

					.audit-status {
						float: right;
						width: 120rpx;
						height: 40rpx;
						line-height: 40rpx;
						text-align: center;
						font-size: 22rpx;
						background: #eafff8;
						border-radius: 20rpx;
					}
				}

				.info_item:active {
					background-color: #f4f4f4;
				}
			}
		}
	}

	.ml5 {
		margin-left: 10rpx;
	}

	.mt10 {
		margin-top: 20rpx;
	}
</style>