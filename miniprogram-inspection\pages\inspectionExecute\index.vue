<template>
  <view class="work-container">
    <topInfo
      title="执行巡检"
      :url="prePageUrl"
      :isShowBack="true"
      backtype="navigateBack"
    ></topInfo>
    <!-- <topInfo title="任务详情" :url="prePageUrl" :isShowBack="true" backtype="navigate"></topInfo> -->
    <!-- 		<view class="tttt" style="z-index:9999;width: 100%;height: 100%;float: right;">
			<button @click="toggle('bottom')" class="mini-btn" type="primary" size="mini"
				style="background-color: #e7edfc;color:#1349e0;width: 80px;z-index:9999;float: right;right:5px;top: 8px;position: fixed;">保存</button>
		</view> -->
    <view class="totalInfo" style="width: 100%; padding: 20rpx">
      <scroll-view style="height: 100%">
        <view class="" style="margin-top: 20rpx">
          <uni-card type="line" is-full>
            <template v-slot:title>
              <view
                class=""
                style="
                  display: flex;
                  flex-direction: row;
                  border-bottom: 2rpx solid #f2f2f2;
                  line-height: 80rpx;
                "
              >
                <view
                  class=""
                  style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  "
                >
                  <!-- 	<image src="../../static/images/inspectionExecute/polygon2x.png"
											style="width: 30rpx;height: 100%;" mode="widthFix"></image> -->
                  <view
                    class=""
                    style="
                      width: 20rpx;
                      height: 40rpx;
                      border-left: 8rpx solid #0e4ae0;
                    "
                  >
                    &nbsp;
                  </view>
                  <view
                    class=""
                    style="
                      font-size: 32rpx;
                      font-weight: 550;
                      height: 80rpx;
                      margin-left: 8rpx;
                    "
                  >
                    设备信息</view
                  >
                </view>
              </view>
            </template>
            <view class="basicInfo">
              <view class="basicInfoDetail">
                <view class="basicInfoTitle">设备名称:</view>
                <view class="basicInfoContent">{{
                  deviceInfo.equipmentName
                }}</view>
              </view>
              <view class="basicInfoDetail">
                <view class="basicInfoTitle">设备编号:</view>
                <view class="basicInfoContent">{{
                  deviceInfo.equipmentCode
                }}</view>
              </view>
              <view class="basicInfoDetail">
                <view class="basicInfoTitle">设备类型:</view>
                <view class="basicInfoContent">{{
                  deviceInfo.equipmentType
                }}</view>
              </view>
              <view class="basicInfoDetail">
                <view class="basicInfoTitle">存放位置:</view>
                <view class="basicInfoContent">{{
                  deviceInfo.equipmentLocationName
                }}</view>
              </view>
            </view>
          </uni-card>
        </view>
      </scroll-view>
    </view>
    <scroll-view
      class="totalInfo"
      scroll-y
      style="padding: 20rpx; width: 100%; flex: 1; height: calc(60vh - 200rpx)"
    >
      <!-- <view class="" style="margin-top: 10px;">
						<uni-card type="line" is-full>
							<template v-slot:title>
								<view class=""
									style="display: flex;flex-direction: row;justify-content: space-between;border-bottom: 1px solid #f2f2f2;line-height: 40px;">
									<view class="" style="display: flex;justify-content: center;align-items: center;">
										<view class=""
											style="width: 10px;height: 20px;;border-left: 4px solid #0e4ae0;">
											&nbsp;
										</view>
										<view class="" style="font-size: 16px;font-weight: 550;margin-left: 4px;">设备信息
										</view>
									</view>
								</view>
							</template>
							<view class="basicInfo">
								<view class="basicInfoDetail">
									<view class="basicInfoTitle">设备名称:</view>
									<view class="basicInfoContent">{{deviceInfo.equipmentName}}</view>
								</view>
								<view class="basicInfoDetail">
									<view class="basicInfoTitle">设备编号:</view>
									<view class="basicInfoContent">{{deviceInfo.equipmentCode}}</view>
								</view>
								<view class="basicInfoDetail">
									<view class="basicInfoTitle">设备类型:</view>
									<view class="basicInfoContent">{{deviceInfo.deviceTypeName}}</view>
								</view>
								<view class="basicInfoDetail">
									<view class="basicInfoTitle">存放位置:</view>
									<view class="basicInfoContent">{{deviceInfo.equipmentLocationName}}</view>
								</view>
							</view>
						</uni-card>
					</view> -->

      <view class="" style="margin-top: 20rpx">
        <uni-card type="line" is-full>
          <template v-slot:title>
            <view
              class=""
              style="
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                border-bottom: 2rpx solid #f2f2f2;
                line-height: 40px;
              "
            >
              <view
                class=""
                style="
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <view
                  class=""
                  style="
                    width: 20rpx;
                    height: 40rpx;
                    border-left: 8rpx solid #0e4ae0;
                  "
                >
                  &nbsp;
                </view>
                <view
                  class=""
                  style="font-size: 32rpx; font-weight: 550; margin-left: 8rpx"
                  >检查项
                </view>
              </view>
            </view>
          </template>
          <view class="checkProject">
            <view
              class="checkProjectDetail"
              v-for="(item, index) in inspectDetailList"
            >
              <view class="">{{ index + 1 }}.{{ item.content }}</view>
              <!-- <view class="" v-if="item.inspectResultMethod== '1'"> -->
              <view class="">
                <radio-group
                  @change="
                    (val) => {
                      radioChange(item, val);
                    }
                  "
                  style="
                    display: flex;
                    flex-direction: row;
                    justify-content: left;
                  "
                >
                  <view class=""
                    ><label class="uni-list-cell uni-list-cell-pd">
                      <radio
                        value="0"
                        :checked="item.result == '0'"
                        style="transform: scale(0.7)"
                      />正常
                    </label></view
                  >
                  <view class=""
                    ><label class="uni-list-cell uni-list-cell-pd">
                      <radio
                        value="1"
                        :checked="item.result == '1'"
                        style="transform: scale(0.7)"
                      />异常
                    </label></view
                  >
                </radio-group>
              </view>
              <!-- 		<view class="" style="display: flex;flex-direction: row;"
										v-if="item.inspectResultMethod== '1'">
										<uni-easyinput class="uni-mt-5" v-model="set2" placeholder="请输入数值"
											@iconClick="iconClick"></uni-easyinput>
										<view class=""
											style="text-align: center;display: flex;justify-content: center;align-items: center; width: 20px;">
											{{item.numericalInformationJson?getUnitDictName(item.numericalInformationJson.unit):''}}
										</view>
									</view> -->
            </view>
          </view>
        </uni-card>
      </view>
      <view class="" style="margin-top: 20rpx">
        <uni-card type="line" is-full>
          <template v-slot:title>
            <view
              class=""
              style="
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                border-bottom: 1px solid #f2f2f2;
                line-height: 80rpx;
              "
            >
              <view
                class=""
                style="
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <view
                  class=""
                  style="
                    width: 20rpx;
                    height: 40rpx;
                    border-left: 8rpx solid #0e4ae0;
                  "
                >
                  &nbsp;
                </view>
                <view
                  class=""
                  style="font-size: 32rpx; font-weight: 550; margin-left: 8rpx"
                  >图片上传
                </view>
              </view>
            </view>
          </template>
          <view class="basicInfo">
            <view class="example-body">
              <uni-file-picker
                limit="6"
                title="最多选择6张图片"
                v-model="imageValue"
                fileMediatype="image"
                mode="grid"
                @select="select"
                @progress="progress"
                @success="successUploadFile"
                @fail="fail"
              ></uni-file-picker>
            </view>
          </view>
        </uni-card>
      </view>
      <view
        class=""
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          margin-top: 40rpx;
          height: 80rpx;
        "
      >
        <!-- <button @click="upChange" class="mini-btn" type="primary" size="mini" style="width: 160px;"
					:disabled="currentTag - 1 < 0">上一个</button>
				<button @click="doneChange" class="mini-btn" type="primary" size="mini"
					style="margin-left: 10px;width: 160px;"
					:disabled="currentTag + 1 >= deviceDetailInfo.length">下一个</button> -->
        <button
          @click="saveDeviceInfo"
          class="mini-btn"
          type="primary"
          size="mini"
          style="
            width: 100%;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
          "
        >
          保存
        </button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
  import { getWorkOrderInspect } from "@/api/inspectionDetail/inspectionDetail.js";
  import { getAccessToken } from "@/utils/auth";
  import {
    getDeviceClassfy,
    submitDevice,
    updateWorkDetail,
    updateInspectDetail,
  } from "@/api/inspectionExecute/inspectionExecute.js";
  import { getEquipmentInspectDetailByHeadPerson } from "@/api/taskDetail/taskDetail.js";
  import { getInspectionTaskStatusDict } from "@/api/system/dict.js";
  import config from "@/config.js";
  export default {
    onLoad(option) {
      const item = JSON.parse(decodeURIComponent(option.item));
      this.deviceInfo = item;
      getEquipmentInspectDetailByHeadPerson(item.workOrderId, item.id).then(
        (res) => {
          if (res.code == 0) {
            this.inspectDetailList = res.data.inspectDetailList;

            for (var i = 0; i < this.inspectDetailList.length; i++) {
              if (!this.inspectDetailList[i].result) {
                this.$set(this.inspectDetailList[i], "result", "0");
              }
            }
            // console.log(this.inspectDetailList)
            this.photoUrls = res.data.photoUrls || [];
            for (var i = 0; i < this.photoUrls.length; i++) {
              var tempImageInfo = {
                name: "",
                url: this.photoUrls[i],
              };
              this.imageValue.push(tempImageInfo);
            }
          }
        }
      );
      // 获取设备详细信息
      // getWorkOrderInspect(item.workOrderId).then(res => {
      // 	if (res.code == 0) {
      // 		this.workOrderInspect = res.data
      // 		if (this.workOrderInspect.length > 0) {
      // 			for (var i = 0; i < this.workOrderInspect.length; i++) {
      // 				var location = this.workOrderInspect[i].equipmentLocation
      // 				var deviceInfo = this.workOrderInspect[i].workOrderInspectVOList
      // 				for (var j = 0; j < deviceInfo.length; j++) {
      // 					this.$set(deviceInfo[j], 'name', deviceInfo[j].equipmentName)
      // 					this.$set(deviceInfo[j], 'location', location)
      // 					this.deviceDetailInfo.push(deviceInfo[j])
      // 				}
      // 			}
      // 			this.deviceInfo = this.deviceDetailInfo[0]
      // 		}
      // 获得设备类型
      getDeviceClassfy(item.equipmentType).then((res) => {
        if (res.code == 0) {
          var deviceTypeName = res.data ? res.data.name : "";
          this.$set(this.deviceInfo, "deviceTypeName", deviceTypeName);
        }
      });

      // 		getEquipmentInspectDetailByHeadPerson(item.workOrderId, this.deviceInfo.id).then(res => {
      // 			if (res.code == 0) {
      // 				this.inspectDetailList = res.data.inspectDetailList
      // 				console.log('this.inspectDetailList',this.inspectDetailList)
      // 			}
      // 		})
      // 	}
      // })

      getInspectionTaskStatusDict("data_unit").then((res) => {
        if (res.code == 0) {
          this.unitDict = res.data;
        }
      });
      this.workOrderId = item.workOrderId;

      this.prePageUrl =
        "/pages/inspectionDetail/index?statusName=" +
        item.statusName +
        "&status=" +
        item.status +
        "&permissionIdentification=" +
        item.permissionIdentification +
        "&workOrderName=" +
        item.workOrderName +
        "&id=" +
        item.workOrderId;
    },
    data() {
      return {
        imageValue: [],
        currentTag: 0,
        workOrderId: "",
        // 单位字典
        unitDict: [],
        // 设备详细信息
        inspectDetailList: [],
        deviceInfo: {},
        deviceDetailInfo: [],
        workOrderInspect: [],
        // 前一个页面url
        prePageUrl: "",
        fengge: "",
        maxCharacters: 200, // 最多字符数
        charCount: 0, // 当前字符数
        currentContent: "",
        value: [],
        otherDescribe: "",
      };
    },
    onReady() {
      // 设置自定义表单校验规则，必须在节点渲染完毕后执行
      // this.$refs.customForm.setRules(this.customRules)
    },
    methods: {
      updateCharCount() {
        this.charCount = this.$data.otherDescribe.length + 1;
      },
      // changeTags(item) {
      // 	this.deviceInfo = item
      // 	getDeviceClassfy(item.equipmentType).then(res => {
      // 		if (res.code == 0) {
      // 			var deviceTypeName = res.data?res.data.name:''
      // 			this.$set(this.deviceInfo, 'deviceTypeName',deviceTypeName)
      // 		}
      // 	})
      // 	getEquipmentInspectDetailByHeadPerson(this.workOrderId, this.deviceInfo.id).then(res => {
      // 		if (res.code == 0) {
      // 			this.inspectDetailList = res.data.inspectDetailList
      // 		}
      // 	})
      // 	this.currentTag = item.index;
      // },
      // 获取上传状态
      select(e) {
        const filePath = e.tempFilePaths[0];
        const file = e.tempFiles[0];
        //获取图片临时路径
        uni.uploadFile({
          // url: 'http://*************:48080/admin-api/infra/file/upload', //【必填】图片上传地址
          url: config.baseUrl_Upload, //【必填】图片上传地址
          filePath, //【必填】（files和filePath选其一）要上传文件资源的路径。
          name: "file", //【必填】上传名字，注意与后台接收的参数名一致
          // header: {"Content-Type": "multipart/form-data"},//设置请求头
          //请求成功，后台返回自己服务器上的图片地址
          header: {
            Authorization: `Bearer ${getAccessToken()}`, // 通过请求头传递 Token
          },
          success: (d) => {
            d = JSON.parse(d.data);
            console.log("d", d);
            if (d.code == 0) {
              // this.imageValue.push(d.data)
              this.imageValue.push({
                url: d.data,
                name: "",
              });
              // console.log(this.imageValue)
              let response = d;
              // uni.showToast({
              // 	icon: `none`,
              // 	title: `上传成功`
              // });
            } else {
              uni.showToast({
                icon: `none`,
                title: d.msg,
              });
            }
          },
        });
        // const file = e.
      },
      // 获取上传进度
      progress(e) {
        console.log(this.imageValue);
        console.log("上传进度：", e);
      },

      // 上传成功
      successUploadFile(e) {
        console.log("上传成功", e);
      },

      // 上传失败
      fail(e) {
        console.log("上传失败：", e);
      },
      iconClick(type) {
        uni.showToast({
          title: `点击了${type === "prefix" ? "左侧" : "右侧"}的图标`,
          icon: "none",
        });
      },
      radioChange: function (item, val) {
        for (var i = 0; i < this.inspectDetailList.length; i++) {
          if (
            this.inspectDetailList[i].devopsWorkOrderItemDetailId ==
            item.devopsWorkOrderItemDetailId
          ) {
            this.$set(this.inspectDetailList[i], "result", val.detail.value);
          }
        }
      },
      // upChange() {
      // 	if (this.currentTag - 1 >= 0) {
      // 		this.currentTag = this.currentTag - 1
      // 		this.deviceInfo = this.deviceDetailInfo[this.currentTag]
      // 		getDeviceClassfy(this.deviceInfo.equipmentType).then(res => {
      // 			if (res.code == 0) {
      // 				var deviceTypeName = res.data ? res.data.name : ''
      // 				this.$set(this.deviceInfo, 'deviceTypeName', deviceTypeName)
      // 			}
      // 		})
      // 		getEquipmentInspectDetailByHeadPerson(this.workOrderId, this.deviceInfo.id).then(res => {
      // 			if (res.code == 0) {
      // 				this.inspectDetailList = res.data.inspectDetailList
      // 			}
      // 		})
      // 	}
      // },
      // doneChange() {
      // 	if (this.currentTag + 1 <= this.deviceDetailInfo.length) {
      // 		this.currentTag = this.currentTag + 1
      // 		this.deviceInfo = this.deviceDetailInfo[this.currentTag]
      // 		getDeviceClassfy(this.deviceInfo.equipmentType).then(res => {
      // 			if (res.code == 0) {
      // 				var deviceTypeName = res.data ? res.data.name : ''
      // 				this.$set(this.deviceInfo, 'deviceTypeName', deviceTypeName)
      // 			}
      // 		})
      // 		getEquipmentInspectDetailByHeadPerson(this.workOrderId, this.deviceInfo.id).then(res => {
      // 			if (res.code == 0) {
      // 				this.inspectDetailList = res.data.inspectDetailList
      // 			}
      // 		})
      // 	}
      // },
      getUnitDictName(value) {
        var dictName = "";
        this.unitDict.forEach((item, index) => {
          if (item.value == value) {
            dictName = item.label;
            return item.label;
          }
        });
        return dictName;
      },
      saveDeviceInfo() {
        var id = this.deviceInfo.id;
        var photoUrlList = [];
        for (var i = 0; i < this.imageValue.length; i++) {
          photoUrlList.push(this.imageValue[i].url);
        }
        var equipmentPhotoVO = {
          id,
          photoUrlList,
        };
        var updateInspectDetailVOList = [];
        for (var i = 0; i < this.inspectDetailList.length; i++) {
          var detail = {
            id: this.inspectDetailList[i].devopsWorkOrderItemDetailId,
            result: this.inspectDetailList[i].result,
          };
          updateInspectDetailVOList.push(detail);
        }
        var updateInspectDetailData = [];
        updateInspectDetailData.push({
          equipmentPhotoVO: equipmentPhotoVO,
          updateInspectDetailVOList: updateInspectDetailVOList,
        });
        // console.log(updateInspectDetailData)
        updateInspectDetail(updateInspectDetailData).then((res) => {
          if (res.code == 0) {
            // uni.navigateTo({
            // 	url:'/pages/inspectionSuccess/index'
            // })
            uni.showToast({
              title: `巡检成功`,
              icon: "success",
              success: () => {
                uni.navigateBack();
              },
            });
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .work-container {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background-color: #fff;
    height: 100vh;
    background-color: #e9e9e9;
    overflow-y: hidden;
    // background: linear-gradient(to bottom,#1850e6 0%,#1850e6 10%,#f3f3f3 90.01%,#f3f3f3 100%)

    // padding: 10px;
  }

  // .totalInfo{
  // 	position: fixed;
  // 	background-image: url(@/static/images/home/<USER>
  // 	background-size: cover;
  // 	z-index: 9999 !important;
  // 	height: 90vh;
  // }

  .basicInfo {
    width: 100%;
  }

  .basicInfoDetail {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .basicInfoTitle {
      // color: rgba(0, 0, 0, 0.4);
      color: #000;
      min-width: 160rpx;
    }

    .basicInfoContent {
      color: rgba(0, 0, 0, 0.4);
      text-align: left;
      height: 100%;
    }
  }

  .container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  .char-count {
    margin-top: -44rpx;
    font-size: 24rpx;
    margin-right: 8rpx;
    color: #888;
    z-index: 9999;
  }
</style>
