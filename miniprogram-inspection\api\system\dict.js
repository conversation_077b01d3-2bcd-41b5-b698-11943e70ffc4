import request from '@/utils/request'


// export function getInspectionTaskStatusDict(pageNo, pageSize, label, dictType) {
// 	var params = 'pageNo=' + pageNo + '&pageSize=' + pageSize + '&label=' + label + '&dictType=' + dictType
// 	return request({
// 		// url: '/system/dict-data/page?pageNo=1&pageSize=100&label=&dictType=Inspection_task_status',
// 		url: '/system/dict-data/page?' + params,
// 		'method': 'GET'
// 	})
// }

// 获取字典数据
export function getInspectionTaskStatusDict(type) {
	var data = {
		type
	}
	return request({
		// url: '/system/dict-data/page?pageNo=1&pageSize=100&label=&dictType=Inspection_task_status',
		url: '/system/dict-data/type',
		'method': 'GET',
		'baseApi':'/app-api',
		'data': data
	})
}