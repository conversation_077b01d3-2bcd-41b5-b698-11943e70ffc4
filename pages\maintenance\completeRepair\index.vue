<template>
  <view class="page">
    <topInfo
      title="维修上报"
      :goback="true"
      :isShowBack="true"
      :isDark="true"
      backtype="navigate"
      backgroundColor="rgba(1.0,1.0,1.0,0.0)"
      :isShadow="false"
    ></topInfo>
    <uni-card class="card">
      <u--form
        class="formStyle"
        label-width="200rpx"
        labelPosition="left"
        :model="repairInfoData"
        :rules="rules"
        ref="uForm"
      >
        <!-- <u-form-item label="维修日期" prop="repairDate" @click="repairDateClick" borderBottom ref="item1">
					<u--input v-model="repairInfoData.repairDate" placeholder='请选择维修日期' border="none" clearable
						inputAlign='right' disabled disabledColor='#ffffff' suffixIcon="arrow-right"></u--input>
				</u-form-item> -->
        <u-form-item
          label="故障类型"
          prop="faultTypeLabel"
          borderBottom
          ref="item2"
          @click="faultTypeClick"
        >
          <u--input
            v-model="repairInfoData.faultTypeLabel"
            placeholder="请选择故障类型"
            border="none"
            clearable
            inputAlign="right"
            disabled
            disabledColor="#ffffff"
            suffixIcon="arrow-right"
          ></u--input>
        </u-form-item>
        <u-form-item
          label="故障原因"
          labelPosition="top"
          prop="faultReason"
          borderBottom
          ref="item3"
          :required="true"
        >
          <u--textarea
            style="margin-top: 5px"
            v-model="repairInfoData.faultReason"
            placeholder="请输入故障原因"
            inputAlign="right"
            count
            maxlength="60"
            @input="filterEmoji"
          ></u--textarea>
        </u-form-item>
        <u-form-item
          label="维修内容"
          labelPosition="top"
          prop="repairContent"
          borderBottom
          ref="item4"
          :required="true"
        >
          <u--textarea
            style="margin-top: 5px"
            v-model="repairInfoData.repairContent"
            placeholder="请输入维修内容"
            inputAlign="right"
            count
            maxlength="60"
            @input="filterEmoji1"
          ></u--textarea>
        </u-form-item>
        <u-form-item
          label="维修程度"
          labelPosition="top"
          prop="repairDegree"
          borderBottom
          ref="item4"
        >
          <u-radio-group v-model="repairInfoData.repairDegree" placement="row">
            <u-radio
              v-for="(item, index) in repairDegreeList"
              :key="index"
              :label="item.label"
              :name="item.value"
              style="margin-right: 10rpx"
            >
            </u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item
          label="维修结果"
          labelPosition="top"
          prop="repairResult"
          borderBottom
          ref="item4"
        >
          <u-radio-group v-model="repairInfoData.repairResult" placement="row">
            <u-radio
              v-for="(item, index) in repairResultList"
              :key="index"
              :label="item.label"
              :name="item.value"
              style="margin-right: 10rpx"
            >
            </u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item
          label="现场照片"
          labelPosition="top"
          prop="photoUrlList"
          borderBottom
          ref="item5"
        >
          <u-upload
            :fileList="fileList1"
            uploadIcon="plus"
            @afterRead="afterRead"
            @delete="deletePic"
            name="1"
            multiple
            :maxCount="9"
            style="margin-top: 10rpx"
          ></u-upload>
        </u-form-item>
      </u--form>
      <u-datetime-picker
        :show="isShowDate"
        v-model="repairInfoData.repairDate"
        mode="date"
        :closeOnClickOverlay="true"
        @close="isShowDate = false"
        @cancel="isShowDate = false"
      >
      </u-datetime-picker>

      <u-action-sheet
        :show="isShowfaultType"
        title="请选择故障类型"
        @close="isShowfaultType = false"
        @select="faultTypeSelect"
      >
        <faultTypeSelectCom @selectData="selectData" />
      </u-action-sheet>
    </uni-card>
    <u-tabbar :fixed="true" :placeholder="true" :safeAreaInsetBottom="true">
      <u-button
        color="#0E4AE0"
        type="primary"
        text="提交"
        style="height: 70rpx; margin: 6px 10px"
        @click="submit"
      ></u-button>
    </u-tabbar>
    <u-toast ref="uToast" />
  </view>
</template>

<script>
  import {
    getMyReportRepairDetail,
    repairComplete,
    repairCompleteBehavior,
  } from "@/api/reportRepair/index.js";
  import faultTypeSelectCom from "./tuiCascadeSelection/index.vue";
  import config from "@/config";
  import { getAccessToken } from "@/utils/auth";
  export default {
    components: {
      faultTypeSelectCom,
    },
    data() {
      return {
        workOrderId: "",
        isShowDate: false,
        isShowfaultType: false,
        repairInfoData: {
          // repairDate: '',
          faultType: "",
          faultTypeLabel: "",
          faultReason: "",
          repairContent: "",
          repairDegree: "0",
          repairResult: "0",
          photoUrlList: [],
        },
        fileList1: [],
        savedData: null,
        serverUrl: config.baseUrl_Upload,
        repairDegreeList: [
          {
            label: "无修",
            name: "0",
            value: "0",
            disabled: false,
          },
          {
            label: "小修",
            value: "1",
            disabled: false,
          },
          {
            label: "一般",
            value: "2",
            disabled: false,
          },
          {
            label: "大修",
            value: "3",
            disabled: false,
          },
        ],
        repairResultList: [
          {
            label: "已解决",
            value: "0",
            disabled: false,
          },
          {
            label: "部分解决",
            value: "1",
            disabled: false,
          },
          {
            label: "未解决",
            value: "2",
            disabled: false,
          },
        ],
        rules: {
          faultReason: {
            type: "string",
            required: true,
            message: "请输入故障原因",
            trigger: ["blur", "change"],
          },
          repairContent: {
            type: "string",
            required: true,
            message: "请输入故障内容",
            trigger: ["blur", "change"],
          },
        },
        customHeaders: {
          Authorization: "Bearer " + getAccessToken(), // 添加身份认证信息
        },
      };
    },
    methods: {
      repairDateClick() {
        this.isShowDate = true;
      },
      faultTypeClick() {
        this.isShowfaultType = true;
      },
      selectData(value, label) {
        this.repairInfoData.faultType = value;
        this.repairInfoData.faultTypeLabel = label;
      },
      // 删除图片
      deletePic(event) {
        this[`fileList${event.name}`].splice(event.index, 1);
      },
      // 新增图片
      async afterRead(event) {
        // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
        let lists = [].concat(event.file);
        console.log(this[`fileList${event.name}`], 1122);
        let fileListLen = this[`fileList${event.name}`].length;
        lists.map((item) => {
          this[`fileList${event.name}`].push({
            ...item,
            status: "uploading",
            message: "上传中",
          });
        });
        for (let i = 0; i < lists.length; i++) {
          const result = await this.uploadFilePromise(lists[i].url);
          let item = this[`fileList${event.name}`][fileListLen];
          this[`fileList${event.name}`].splice(
            fileListLen,
            1,
            Object.assign(item, {
              status: "success",
              message: "",
              url: result,
            })
          );
          fileListLen++;
        }
      },
      uploadFilePromise(url) {
        return new Promise((resolve, reject) => {
          let a = uni.uploadFile({
            url: this.serverUrl,
            filePath: url,
            name: "file",
            header: this.customHeaders,
            formData: {
              user: "test",
            },
            success: (res) => {
              setTimeout(() => {
                const { data, code } = JSON.parse(res.data);
                if (code == 0) resolve(data);
                else reject();
              }, 1000);
            },
          });
        });
      },
      submit() {
        this.$refs.uForm
          .validate()
          .then(async () => {
            const params = {
              workOrderId: this.workOrderId,
              ...this.repairInfoData,
              photoUrlList: this.fileList1.map((item) => item.url),
            };
            const { code } = await repairComplete(params);
            if (!code) {
              this.$refs.uToast.show({
                message: "提交成功",
                type: "success",
                complete: async () => {
                  const { code } = await repairCompleteBehavior({
                    id: this.workOrderId,
                  });
                  if (!code)
                    // uni.navigateTo({
                    //   url: "/pages/maintenance/pendingOrders/index?type=全部工单12&workType=2",
                    // });
                    uni.navigateBack({
                      delta: 2,
                    });
                },
              });
            } else {
              this.$refs.uToast.show({
                type: "error",
                message: "提交失败",
              });
            }
          })
          .catch((err) => {
            this.$refs.uToast.show({
              type: "error",
              message: "请填写所有必填信息",
            });
          });
      },
      filterEmoji(val) {
        let emoji =
          /(\ud83c[\udf00-\udfff])|(\ud83d[\udc00-\ude4f])|(\ud83d[\ude80-\udeff])/g;
        // 如果包含表情，则将输入框的值还原为上一个状态
        if (val.match(emoji)) {
          this.$nextTick(() => {
            this.repairInfoData.faultReason = val.replace(emoji, "");
          });
        }
      },
      filterEmoji1(val) {
        let emoji =
          /(\ud83c[\udf00-\udfff])|(\ud83d[\udc00-\ude4f])|(\ud83d[\ude80-\udeff])/g;
        // 如果包含表情，则将输入框的值还原为上一个状态
        if (val.match(emoji)) {
          this.$nextTick(() => {
            this.repairInfoData.repairContent = val.replace(emoji, "");
          });
        }
      },
    },
    mounted() {},
    onLoad(option) {
      this.workOrderId = option.workOrderId;
    },
  };
</script>

<style lang="scss" scoped>
  .page {
    height: 1200rpx;
    background-image: url(@/static/images/home/<USER>
    background-size: contain;
    /* 背景图等比缩放充整个容器 */
    background-position: center;
    /* 背景图居中对齐 */
  }

  .content {
    width: 100%;
    height: 100%;

    .content-item {
      margin-top: 20rpx;
      display: flex;
      justify-content: space-between;
      width: 100%;
      font-family: MiSans, MiSans;
      font-size: 28rpx;
      color: rgba(0, 0, 0, 0.9);
      line-height: 30rpx;
      text-align: right;
      font-style: normal;
      text-transform: none;
      border-bottom: 1rpx solid #e7e7e7;
      padding-bottom: 20rpx;

      .item-left {
        // width: 40%;
      }

      .item-right {
        width: 75%;
        // padding-left: 30rpx;
        height: 100%;

        img {
          vertical-align: bottom;
          margin-right: 5rpx;
        }
      }
    }

    :nth-last-child(1) {
      border: 0;
    }
  }
</style>
