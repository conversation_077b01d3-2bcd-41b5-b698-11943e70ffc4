<template>
  <view class="work-container">
    <!-- <topInfo title="设备维保" url="/pages/index" :isShowBack="true" backtype="switch"></topInfo> -->
    <view
      class="workinfo"
      style="
        top: 0;
        z-index: 999 !important;
        position: fixed;
        width: 100%;
        background-color: #eeeeee;
        padding: 10px;
      "
    >
      <uni-nav-bar
        :fixed="true"
        shadow
        background-color="transparent"
        status-bar
        title="设备维保"
        left-icon="left"
        @clickLeft="returnFirstPage"
      />
      <view class="search-title" style="background-color: transparent">
        <uni-search-bar
          :radius="100"
          class="searchBar"
          @confirm="search"
          :focus="false"
          v-model="requestParam.workOrderName"
          clearButton="auto"
          cancelButton="none"
          placeholder="请输入任务名称"
        >
        </uni-search-bar>
        <button
          @click="searchData"
          size="default"
          type="default"
          style="
            display: flex;
            line-height: 68rpx;
            font-size: 30rpx;
            width: 136rpx;
            height: 68rpx;
            text-align: center;
            color: #343536;
            background-color: #f6f8fe;
            bordercolor: #f6f8fe;
          "
        >
          搜索
        </button>
      </view>
      <view class="totalInfo">
        <uni-card title="工单统计" type="line" is-full>
          <uni-grid
            :column="6"
            :highlight="true"
            :showBorder="false"
            borderColor="#81d3f8"
            style="width: 100%"
            :square="false"
          >
            <uni-grid-item
              v-for="(item, index) in list"
              :index="index"
              :key="index"
            >
              <view
                class="grid-item-box"
                :style="{
                  color: currentData == item.status ? '#0e4ae0' : '#666666',
                }"
                @click="changeGrid(item)"
              >
                <view class="infoNum">{{ item.countTotal }}</view>
                <view class="text">{{
                  item.status == 0 ? "全部" : getDictName(item.status)
                }}</view>
              </view>
            </uni-grid-item>
          </uni-grid>
        </uni-card>
      </view>
    </view>
    <!-- 		<view class="search-title" style="z-index: 999 !important;position: fixed;">
			<uni-search-bar :radius="100" class="searchBar" @confirm="search" :focus="true"
				v-model="requestParam.workOrderName" clearButton="auto" cancelButton="none" placeholder="请输入任务名称">
			</uni-search-bar>
			<button @click="searchData" size="default" type="default"
				style="display: flex;line-height: 34px;;font-size: 30rpx;width: 68px;height: 34px;text-align:center;color: #343536;background-color: #f6f8fe;borderColor:#f6f8fe">搜索</button>
		</view>
		<view class="totalInfo" style="z-index: 999 !important;position: fixed;top: 100px;width: 100%;">
			<uni-card title="工单统计" type="line" is-full>
				<uni-grid :column="6" :highlight="true" :showBorder="false" borderColor="#81d3f8" style="width: 100%;"
					:square="false">
					<uni-grid-item v-for="(item, index) in list" :index="index" :key="index">
						<view class="grid-item-box" :style="{color:currentData == item.status ? '#0e4ae0' : '#666666',}"
							@click="changeGrid(item)">
							<view class="infoNum">{{item.countTotal}}</view>
							<view class="text">{{item.status == 0?'全部':getDictName(item.status)}}</view>
						</view>
					</uni-grid-item>
				</uni-grid>
			</uni-card>
		</view> -->

    <scroll-view
      class="totalDetailInfo"
      scroll-y
      @scrolltolower="scrolltolowerFn"
    >
      <uni-card
        type="line"
        is-full
        style="margin-top: 20rpx"
        v-for="(item, index) in workOrder"
      >
        <template v-slot:title>
          <view class="slot-collapse">
            <view
              class=""
              style="
                display: flex;
                justify-content: flex-start;
                flex-direction: row;
                align-items: center;
              "
            >
              <view
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 50rpx;
                  height: 100%;
                "
              >
                <image
                  src="../../static/images/inspection/group1.png"
                  mode="widthFix"
                  style="width: 40rpx; height: 100%; margin-top: 4rpx"
                ></image>
              </view>
              <view
                style="
                  font-size: 16px;
                  margin-left: 10rpx;
                  font-weight: 600;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                {{ item.workOrderName }}
              </view>
            </view>
            <view
              style="
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                margin-right: 20px;
              "
            >
              <uni-tag
                style="margin-left: 10rpx"
                :text="getDictName(item.status)"
                type="warning"
                :style="{
                  backgroundColor:
                    getDictName(item.status) == '待派单'
                      ? '#fff6e6'
                      : getDictName(item.status) == '待执行'
                      ? '#e7edfc'
                      : getDictName(item.status) == '已逾期'
                      ? '#feebec'
                      : getDictName(item.status) == '已退回'
                      ? '#e6e6e6'
                      : getDictName(item.status) == '已完成'
                      ? '#e6f8f0'
                      : '',
                  color:
                    getDictName(item.status) == '待派单'
                      ? '#eeaa43'
                      : getDictName(item.status) == '待执行'
                      ? '#0e4ae0'
                      : getDictName(item.status) == '已逾期'
                      ? '#f64654'
                      : getDictName(item.status) == '已退回'
                      ? '#313131'
                      : getDictName(item.status) == '已完成'
                      ? '#20b979'
                      : '',
                  borderColor:
                    getDictName(item.status) == '待派单'
                      ? '#fff6e6'
                      : getDictName(item.status) == '待执行'
                      ? '#e7edfc'
                      : getDictName(item.status) == '已逾期'
                      ? '#feebec'
                      : getDictName(item.status) == '已退回'
                      ? '#e6e6e6'
                      : getDictName(item.status) == '已完成'
                      ? '#e6f8f0'
                      : '',
                }"
              ></uni-tag>
              <!-- <uni-tag style="margin-left: 10rpx;" :text="item.status" type="warning"
								:custom-style="{backgroundColor: item.status == '待派单'?'#fff6e6': item.status == '待执行'?'#e7edfc':item.status == '已逾期'?'#feebec': item.status == '已退回'?'#e6e6e6': item.status == '已完成'?'#e6f8f0':'',
								color: item.status == '待派单'?'#eeaa43': item.status == '待执行'?'#0e4ae0':item.status == '已逾期'?'#f64654': item.status == '已退回'?'#313131': item.status == '已完成'?'#20b979':'',borderColor: item.status == '待派单'?'#fff6e6': item.status == '待执行'?'#e7edfc':item.status == '已逾期'?'#feebec': item.status == '已退回'?'#e6e6e6': item.status == '已完成'?'#e6f8f0':''}"></uni-tag> -->
            </view>
          </view>
        </template>
        <view class="basicInfoTopStyle" @click="getDetail(item)">
          <view class="basicInfoDetail">
            <view class="basicInfoTitle">任务编号：</view>
            <view class="basicInfoContent">{{ item.workOrderCode }}</view>
          </view>
          <view class="basicInfoDetail">
            <view class="basicInfoTitle">执行日期：</view>
            <view class="basicInfoContent">{{ item.frequencyDate }}</view>
          </view>
          <view class="basicInfoDetail">
            <view class="basicInfoTitle">执行时间：</view>
            <view class="basicInfoContent">{{ item.frequencyTime }}</view>
          </view>
          <view class="basicInfoDetail">
            <view class="basicInfoTitle">维保类型：</view>
            <view class="basicInfoContent">{{
              getInspectionDictName(item.planType)
            }}</view>
          </view>
        </view>
      </uni-card>
      <view v-if="loading" class="loading">加载中...</view>
      <view v-if="noMoreData" class="no-more">没有更多数据了</view>
    </scroll-view>
  </view>
</template>

<script>
  // import topInfo from '@/components/top/index.vue'
  import {
    getInspectionInfo,
    getStatistics,
  } from "@/api/inspection/inspection.js";

  import { getInspectionTaskStatusDict } from "@/api/system/dict.js";
  export default {
    onLoad: function (option) {
      //option为object类型，会序列化上个页面传递的参数
      console.log(getApp().globalData);
      getInspectionTaskStatusDict("Inspection_task_status").then((res) => {
        if (res.code == 0) {
          this.inspectionDict = res.data;
          // console.log(res)
        }
      });
      if (
        option.status != null &&
        option.status != undefined &&
        option.status != "0"
      ) {
        this.$set(this.requestParam, "status", option.status);
      }
      if (option.status != null && option.status != undefined) {
        this.currentData = option.status;
      }
      this.$set(
        this.requestParam,
        "permissionIdentification",
        option.menuPermission
      );

      getInspectionInfo(this.requestParam).then((res) => {
        if (res.code == 0) {
          this.workOrder = res.data.list;
          // console.log(this.workOrder)
        }
      });
      // 维保类型
      getInspectionTaskStatusDict("Inspection_type").then((res) => {
        if (res.code == 0) {
          this.inspectionTypeDict = res.data;
        }
      });

      getStatistics().then((res) => {
        if (res.code == 0) {
          // console.log(res)
          if (res.data.length > 0) {
            var numList = res.data;
            var total = 0;
            var inspectionMenuPermissionList = [];
            console.log("维保", res);
            console.log("维保option", option);
            for (var i = 0; i < numList.length; i++) {
              if (
                numList[i].permissionIdentification == option.menuPermission
              ) {
                inspectionMenuPermissionList =
                  numList[i].workOrderStatisticsList;
                total = numList[i].total;
              }
            }

            var params = {
              countTotal: total,
              status: 0,
            };
            this.list = inspectionMenuPermissionList;
            this.list.unshift(params);
            for (var i = 0; i < this.list.length; i++) {
              if (this.list[i].status == this.currentData) {
                this.currentCountTotal = this.list[i].countTotal;
              }
            }
          } else {
            var params = {
              countTotal: 0,
              status: 0,
            };
            this.list.unshift(params);
          }
        }
      });
    },
    onPullDownRefresh() {
      // uni.startPullDownRefresh();
      console.log("refresh");
      // setTimeout(function() {
      // 	uni.stopPullDownRefresh();
      // }, 1000);
    },
    // 滚动到达底部事件
    onReachBottom() {
      if (this.requestParam.pageSize < this.currentCountTotal) {
        var pageSize = this.requestParam.pageSize + 10;
        this.$set(this.requestParam, "pageSize", pageSize);
        getInspectionInfo(this.requestParam).then((res) => {
          if (res.code == 0) {
            this.workOrder = res.data.list;
          }
        });
      }
    },
    data() {
      return {
        currentCountTotal: 10,
        requestParam: {
          pageNo: 1,
          pageSize: 10,
          permissionIdentification: "",
          status: "",
          workOrderName: "",
        },
        loading: false, // 加载状态
        noMoreData: false, // 是否还有更多数据
        // 维保类型
        inspectionTypeDict: [],
        // 维保任务状态
        inspectionDict: [],
        currentData: 0,
        list: [],
        workOrder: [],
      };
    },
    methods: {
      searchData() {
        getInspectionInfo(this.requestParam).then((res) => {
          if (res.code == 0) {
            this.workOrder = res.data.list;
          }
        });
      },
      getDetail(item) {
        uni.navigateTo({
          url:
            "/pages/maintain/myMaintainDetail/myMaintainDetailTwo/myMaintainDetailTwo?id=" +
            item.id +
            "&statusName=" +
            this.getDictName(item.status) +
            "&workOrderName=" +
            item.workOrderName +
            "&status=" +
            this.currentData +
            "&permissionIdentification=" +
            this.requestParam.permissionIdentification,
        });
      },
      getDictName(value) {
        var dictName = "";
        this.inspectionDict.forEach((item, index) => {
          if (item.value == value) {
            dictName = item.label;
            return item.label;
          }
        });
        return dictName;
      },
      getDictStatus(name, inspectionDict) {
        var dictStatus = "";
        inspectionDict.forEach((item, index) => {
          if (item.name == name) {
            dictStatus = item.status;
            return item.status;
          }
        });
        return dictStatus;
      },
      getInspectionDictName(value) {
        var dictName = "";
        this.inspectionTypeDict.forEach((item, index) => {
          if (item.value == value) {
            dictName = item.label;
            return item.label;
          }
        });
        return dictName;
      },
      changeGrid(item) {
        // console.log(item)
        this.currentData = item.status;
        this.currentCountTotal = item.countTotal;
        if (item.status != 0) {
          this.$set(this.requestParam, "status", item.status);
        } else {
          this.$set(this.requestParam, "status", "");
        }
        getInspectionInfo(this.requestParam).then((res) => {
          if (res.code == 0) {
            this.workOrder = res.data.list;
          }
        });
      },
      returnFirstPage() {
        uni.navigateBack();
      },
      // Reachbottom(){
      // 	console.log('到底了')
      // 	if(this.requestParam.pageSize < this.currentCountTotal){
      // 		var pageSize = this.requestParam.pageSize +10
      // 		this.$set(this.requestParam, 'pageSize',pageSize)
      // 		getInspectionInfo(this.requestParam).then(res => {
      // 			if (res.code == 0) {
      // 				this.workOrder = res.data.list
      // 			}
      // 		})
      // 	}
      // }
      scrolltolowerFn() {
        console.log("到底了");
        if (this.requestParam.pageSize < this.currentCountTotal) {
          var pageSize = this.requestParam.pageSize + 10;
          this.$set(this.requestParam, "pageSize", pageSize);
          this.loading = true;
          getInspectionInfo(this.requestParam).then((res) => {
            if (res.code == 0) {
              this.workOrder = res.data.list;
            }
            this.loading = false;
          });
        } else {
          this.noMoreData = true;
          this.loading = false;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .work-container {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    // min-height: 100%;
    height: 100vh;
    background-color: #e9e9e9;
  }

  .workinfo {
    background-image: url(@/static/images/home/<USER>
    background-size: cover;

    /deep/ .uni-navbar--border {
      border: 0rpx;
    }

    /deep/ .uni-navbar--shadow {
      box-shadow: 0rpx 0rpx 0rpx #c2e0fa;
    }
  }

  /deep/ .uni-card .uni-card__content {
    padding: 0 20rpx 20rpx 20rpx !important;
    font-size: 28rpx;
    color: #6a6a6a;
    line-height: 44rpx;
  }

  /deep/ .uni-card {
    margin: 20rpx;
    padding: 0 !important;
    border-radius: 8rpx;
    overflow: hidden;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
      Microsoft YaHei, SimSun, sans-serif;
    background-color: #fff;
    flex: 1;
  }

  /deep/.uni-card
    .uni-card__header
    .uni-card__header-content
    .uni-card__header-content-title {
    font-size: 32rpx;
    color: #3a3a3a;
    font-weight: 550;
  }

  /deep/ .uni-card .uni-card__header {
    display: flex;
    border-bottom: 2rpx #ebeef5 solid;
    flex-direction: row;
    align-items: center;
    // padding: 10px;
    overflow: hidden;
    background-color: #f6f8fe;
  }

  view {
    font-size: 28rpx;
    line-height: inherit;
  }

  .text {
    text-align: center;
    font-size: 20rpx;
    margin-top: 10rpx;
  }

  .search-title {
    margin-top: 30rpx;
    display: flex;
    height: 80rpx;
    width: 100%;
    align-items: center;
    justify-content: center;
    padding: 0 20rpx 0 0;
  }

  .searchBar {
    width: 100%;
  }

  .totalInfo {
    margin-top: 20rpx;
    padding: 12rpx;
    // position: absolute;
    // top: 100px;
    // width: 100%;
    // height: 100%;
  }

  .totalDetailInfo {
    // padding: 0 6px 6px 6px;
    // margin-top: 360rpx;
    margin-top: 460rpx;
    //margin-top: 35vh;
    overflow: scroll;
    padding: 34rpx;
    // height: 60vh;
    .loading,
    .no-more {
      text-align: center;
      padding: 20px;
      color: #999;
    }
  }

  .infoNum {
    font-size: 36rpx;
    font-weight: 550;
    text-align: center;
    margin-top: 26rpx;
  }

  .slot-collapse {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 80rpx;
    align-items: center;
    padding: 0 10rpx;
    justify-content: space-between;
  }

  .slot-content {
    width: 100%;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: left;
  }

  .basicInfoTopStyle {
    background-color: #f6f8fe;
    padding: 20rpx;
  }

  .basicInfoDetail {
    display: flex;
    flex-direction: row;
    justify-content: left;
    align-items: center;
    margin-top: 8rpx;
  }

  .basicInfoTitle {
    color: rgba(0, 0, 0, 0.4);
  }

  .basicInfoContent {
    color: #343536;
  }

  .processInfo {
    margin-top: 20rpx;
    background: rgba(14, 74, 224, 0.04);
  }

  .bottom-button {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    height: 80rpx;
    margin-top: 10rpx;
  }
</style>
